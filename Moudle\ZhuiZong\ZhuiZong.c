#include "ZhuiZong.h"
#include <stdio.h>

// 宏定义
#ifndef ABS
#define ABS(x) ((x) > 0 ? (x) : -(x))
#endif

/**********************************************************
***	K230D红色追踪系统模块实现
***	编写作者：Alex (工程师)
***	功能：轻量级非阻塞红色目标追踪
***	版本：V3.0 - EMMV5云台控制版
***	日期：2025-07-29
**********************************************************/

// 全局变量
static LightweightTracker_t g_tracker = {320, 240, 0, false, TRACK_IDLE, 0, 0, 0};
static ZhuiZong_Config_t g_config;
static ZhuiZong_Stats_t g_stats = {0};
static bool g_initialized = false;
static bool g_running = false;
static bool g_paused = false;
static bool g_debug_mode = false;

// 任务调度计数器
static uint32_t data_counter = 0;
static uint32_t control_counter = 0;
static uint32_t timeout_counter = 0;

// 状态字符串
static const char* state_strings[] = {"IDLE", "SEARCH", "TRACK", "LOST"};

/**********************************************************
*** 核心API接口实现
**********************************************************/

/**
  * @brief 初始化追踪系统
  */
HAL_StatusTypeDef ZhuiZong_Init(ZhuiZong_Config_t* config)
{
    // 设置默认配置
    g_config.image_center_x = 320;
    g_config.image_center_y = 240;
    g_config.dead_zone_x = 20;
    g_config.dead_zone_y = 20;
    g_config.control_gain = 3;
    g_config.max_speed_x = 10;
    g_config.max_speed_y = 5;
    g_config.timeout_ms = 200;
    g_config.x_motor_addr = 1;
    g_config.y_motor_addr = 1;
    g_config.x_uart = NULL;
    g_config.y_uart = NULL;
    
    // 如果用户提供了配置，则覆盖默认值
    if (config != NULL) {
        g_config = *config;
    }
    
    // 初始化追踪器
    g_tracker.target_x = g_config.image_center_x;
    g_tracker.target_y = g_config.image_center_y;
    g_tracker.valid = false;
    g_tracker.state = TRACK_IDLE;
    g_tracker.last_update = HAL_GetTick();
    g_tracker.valid_count = 0;
    g_tracker.lost_count = 0;
    
    // 重置统计信息
    ZhuiZong_ResetStats();
    
    g_initialized = true;
    
    // 暂时禁用printf调试输出，避免UART冲突
    // if (g_debug_mode) {
    //     printf("ZhuiZong: Initialized successfully\r\n");
    // }
    
    return HAL_OK;
}

/**
  * @brief 追踪系统主循环更新
  */
void ZhuiZong_Update(void)
{
    if (!g_initialized || !g_running || g_paused) {
        return;
    }
    
    g_stats.total_updates++;
    
    data_counter++;
    control_counter++;
    timeout_counter++;
    
    // 任务1: 数据处理 (每100次循环，约100Hz)
    if (data_counter >= 100) {
        ZhuiZong_ProcessData();
        data_counter = 0;
    }
    
    // 任务2: 超时检测 (每2000次循环检查一次)
    if (timeout_counter >= 2000) {
        ZhuiZong_CheckTimeout();
        timeout_counter = 0;
    }
    
    // 任务3: 追踪控制 (每500次循环，约20Hz)
    if (control_counter >= 500) {
        ZhuiZong_UpdateControl();
        control_counter = 0;
    }
}

/**
  * @brief 启动追踪
  */
void ZhuiZong_Start(void)
{
    if (!g_initialized) {
        return;
    }
    
    g_running = true;
    g_paused = false;
    g_tracker.state = TRACK_SEARCH;
    
    // if (g_debug_mode) {
    //     printf("ZhuiZong: Started\r\n");
    // }
}

/**
  * @brief 停止追踪
  */
void ZhuiZong_Stop(void)
{
    g_running = false;
    g_paused = false;
    g_tracker.state = TRACK_IDLE;
    
    // 停止电机
    ZhuiZong_SendMotorCommand(0, 0);
    
    // if (g_debug_mode) {
    //     printf("ZhuiZong: Stopped\r\n");
    // }
}

/**
  * @brief 暂停追踪
  */
void ZhuiZong_Pause(void)
{
    g_paused = true;
    
    // 停止电机
    ZhuiZong_SendMotorCommand(0, 0);
    
    // if (g_debug_mode) {
    //     printf("ZhuiZong: Paused\r\n");
    // }
}

/**
  * @brief 恢复追踪
  */
void ZhuiZong_Resume(void)
{
    if (g_running) {
        g_paused = false;
        
        // if (g_debug_mode) {
        //     printf("ZhuiZong: Resumed\r\n");
        // }
    }
}

/**********************************************************
*** 状态查询接口实现
**********************************************************/

/**
  * @brief 获取当前追踪状态
  */
TrackingState_t ZhuiZong_GetState(void)
{
    return g_tracker.state;
}

/**
  * @brief 获取当前目标坐标
  */
bool ZhuiZong_GetTargetPos(int16_t* x, int16_t* y)
{
    if (x != NULL) *x = g_tracker.target_x;
    if (y != NULL) *y = g_tracker.target_y;
    return g_tracker.valid;
}

/**
  * @brief 获取控制误差
  */
void ZhuiZong_GetError(int16_t* error_x, int16_t* error_y)
{
    if (error_x != NULL) *error_x = g_stats.last_error_x;
    if (error_y != NULL) *error_y = g_stats.last_error_y;
}

/**
  * @brief 获取统计信息
  */
ZhuiZong_Stats_t* ZhuiZong_GetStats(void)
{
    return &g_stats;
}

/**
  * @brief 获取状态字符串
  */
const char* ZhuiZong_GetStateString(void)
{
    return state_strings[g_tracker.state];
}

/**********************************************************
*** 配置接口实现
**********************************************************/

/**
  * @brief 设置图像中心坐标
  */
void ZhuiZong_SetImageCenter(uint16_t center_x, uint16_t center_y)
{
    g_config.image_center_x = center_x;
    g_config.image_center_y = center_y;
}

/**
  * @brief 设置死区大小
  */
void ZhuiZong_SetDeadZone(uint16_t dead_zone_x, uint16_t dead_zone_y)
{
    g_config.dead_zone_x = dead_zone_x;
    g_config.dead_zone_y = dead_zone_y;
}

/**
  * @brief 设置最大速度
  */
void ZhuiZong_SetMaxSpeed(int16_t max_speed_x, int16_t max_speed_y)
{
    g_config.max_speed_x = max_speed_x;
    g_config.max_speed_y = max_speed_y;
}

/**
  * @brief 设置控制增益
  */
void ZhuiZong_SetControlGain(uint8_t gain)
{
    if (gain >= 1 && gain <= 10) {
        g_config.control_gain = gain;
    }
}

/**********************************************************
*** 调试接口实现
**********************************************************/

/**
  * @brief 启用/禁用调试模式
  */
void ZhuiZong_SetDebugMode(bool enable)
{
    g_debug_mode = enable;
}

/**
  * @brief 手动设置目标坐标
  */
void ZhuiZong_SetManualTarget(int16_t x, int16_t y)
{
    g_tracker.target_x = x;
    g_tracker.target_y = y;
    g_tracker.valid = true;
    g_tracker.last_update = HAL_GetTick();
    g_tracker.state = TRACK_ACTIVE;
}

/**
  * @brief 重置统计信息
  */
void ZhuiZong_ResetStats(void)
{
    g_stats.total_updates = 0;
    g_stats.valid_targets = 0;
    g_stats.lost_targets = 0;
    g_stats.control_commands = 0;
    g_stats.last_control_time = 0;
    g_stats.last_error_x = 0;
    g_stats.last_error_y = 0;
    g_stats.last_speed_x = 0;
    g_stats.last_speed_y = 0;
}

/**********************************************************
*** 内部函数实现
**********************************************************/

/**
  * @brief 数据处理
  */
void ZhuiZong_ProcessData(void)
{
    int16_t x, y;
    if (K230D_GetCoordinates(&x, &y)) {
        // 坐标有效性检查
        if (x >= 0 && x <= 640 && y >= 0 && y <= 480) {
            g_tracker.target_x = x;
            g_tracker.target_y = y;
            g_tracker.valid = true;
            g_tracker.last_update = HAL_GetTick();
            g_tracker.valid_count++;
            g_stats.valid_targets++;

            // 状态切换到追踪
            if (g_tracker.state != TRACK_ACTIVE) {
                g_tracker.state = TRACK_ACTIVE;

                // if (g_debug_mode) {
                //     printf("ZhuiZong: Target acquired at (%d, %d)\r\n", x, y);
                // }
            }
        }
        K230D_ClearUpdateFlag();
    }
}

/**
  * @brief 超时检测
  */
void ZhuiZong_CheckTimeout(void)
{
    uint32_t current_time = HAL_GetTick();
    if (current_time - g_tracker.last_update > g_config.timeout_ms) {
        // 数据超时，切换到丢失状态
        if (g_tracker.state == TRACK_ACTIVE) {
            g_tracker.state = TRACK_LOST;
            g_tracker.lost_count++;
            g_tracker.valid = false;
            g_stats.lost_targets++;

            // if (g_debug_mode) {
            //     printf("ZhuiZong: Target lost (timeout)\r\n");
            // }
        }
    }
}

/**
  * @brief 追踪控制更新
  */
void ZhuiZong_UpdateControl(void)
{
    if (g_tracker.state == TRACK_ACTIVE && g_tracker.valid) {
        // 计算控制误差
        int16_t error_x = g_tracker.target_x - g_config.image_center_x;
        int16_t error_y = g_tracker.target_y - g_config.image_center_y;

        // 更新统计信息
        g_stats.last_error_x = error_x;
        g_stats.last_error_y = error_y;

        // 死区控制，避免小幅抖动
        int16_t speed_x = 0, speed_y = 0;
        if (error_x > g_config.dead_zone_x || error_x < -g_config.dead_zone_x) {
            speed_x = error_x / g_config.control_gain;
        }
        if (error_y > g_config.dead_zone_y || error_y < -g_config.dead_zone_y) {
            speed_y = error_y / g_config.control_gain;
        }

        // 速度限制
        if (speed_x > g_config.max_speed_x) speed_x = g_config.max_speed_x;
        if (speed_x < -g_config.max_speed_x) speed_x = -g_config.max_speed_x;
        if (speed_y > g_config.max_speed_y) speed_y = g_config.max_speed_y;
        if (speed_y < -g_config.max_speed_y) speed_y = -g_config.max_speed_y;

        // 更新统计信息
        g_stats.last_speed_x = speed_x;
        g_stats.last_speed_y = speed_y;

        // 发送电机控制命令
        ZhuiZong_SendMotorCommand(speed_x, speed_y);

        // if (g_debug_mode && (speed_x != 0 || speed_y != 0)) {
        //     printf("ZhuiZong: Control X=%d, Y=%d (Error: %d, %d)\r\n",
        //            speed_x, speed_y, error_x, error_y);
        // }
    } else {
        // 停止电机
        ZhuiZong_SendMotorCommand(0, 0);
    }
}

/**
  * @brief 发送电机控制命令
  */
void ZhuiZong_SendMotorCommand(int16_t speed_x, int16_t speed_y)
{
    static uint32_t last_send = 0;
    uint32_t current_time = HAL_GetTick();

    // 限制发送频率到20Hz，避免阻塞
    if (current_time - last_send < 50) {
        return;
    }

    // 更新统计信息
    g_stats.control_commands++;
    g_stats.last_control_time = current_time;

    // EMMV5云台控制
    uint8_t x_addr = g_config.x_motor_addr;
    uint8_t y_addr = g_config.y_motor_addr;

    // 速度转换：-100到100 → 0到500RPM
    uint16_t x_vel = (uint16_t)ABS(speed_x) * 5;
    uint16_t y_vel = (uint16_t)ABS(speed_y) * 5;

    // 方向：根据用户修改，X轴正方向为CCW(1)，负方向为CW(0)
    uint8_t x_dir = (speed_x >= 0) ? 1 : 0;
    uint8_t y_dir = (speed_y >= 0) ? 0 : 1;

    // 发送X轴控制命令
    if (g_config.x_uart != NULL) {
        if (speed_x != 0) {
            Emm_V5_Vel_Control(g_config.x_uart, x_addr, x_dir, x_vel, 10, false);
        } else {
            Emm_V5_Stop_Now(g_config.x_uart, x_addr, false);
        }
    }

    // 发送Y轴控制命令
    if (g_config.y_uart != NULL) {
        if (speed_y != 0) {
            Emm_V5_Vel_Control(g_config.y_uart, y_addr, y_dir, y_vel, 10, false);
        } else {
            Emm_V5_Stop_Now(g_config.y_uart, y_addr, false);
        }
    }

    last_send = current_time;
}
