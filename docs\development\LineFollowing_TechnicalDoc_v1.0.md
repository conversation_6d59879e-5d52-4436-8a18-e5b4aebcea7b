# 循迹功能技术实现文档
**Technical Implementation Document - Line Following System**

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| **文档标题** | 循迹功能技术实现与API文档 |
| **版本号** | V1.0 |
| **编写日期** | 2025-07-29 |
| **负责人** | Alex (工程师) |
| **项目代号** | LineFollowing_TechnicalDoc |

---

## 2. 系统架构概述

### 2.1 整体架构
循迹系统采用分层模块化架构，包含以下核心模块：

```
┌─────────────────────────────────────────┐
│           应用层 (Application)           │
│  ┌─────────────────────────────────────┐ │
│  │      循迹主控制器 (LineFollowing)    │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│            控制层 (Control)             │
│  ┌─────────┐ ┌─────────┐ ┌─────────────┐ │
│  │角度控制 │ │巡线控制 │ │电机控制     │ │
│  │AngleCtrl│ │LineCtrl │ │MotorCtrl    │ │
│  └─────────┘ └─────────┘ └─────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│           数据层 (Data)                 │
│  ┌─────────────────────────────────────┐ │
│  │      传感器数据融合 (SensorFusion)  │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│        硬件抽象层 (HAL)                 │
│  ┌─────────┐ ┌─────────┐ ┌─────────────┐ │
│  │JY901S   │ │灰度传感器│ │DRV8871      │ │
│  │陀螺仪   │ │阵列     │ │电机驱动     │ │
│  └─────────┘ └─────────┘ └─────────────┘ │
└─────────────────────────────────────────┘
```

### 2.2 核心特性
- **多模式控制**: 支持纯角度、纯巡线、融合三种控制模式
- **实时性能**: 20Hz控制频率，50ms响应时间
- **高精度控制**: 直线偏差±2°，转弯精度±5°
- **智能融合**: JY901S角度数据与8路灰度传感器数据融合
- **模块化设计**: 各模块独立，便于调试和维护

---

## 3. 核心算法实现

### 3.1 角度控制算法

#### 3.1.1 PID控制器
```c
float AngleControl_Update(AngleControl_t* ctrl, float target, float current)
{
    // 角度误差计算 (处理角度环绕)
    float error = target - current;
    if (error > 180.0f) error -= 360.0f;
    if (error < -180.0f) error += 360.0f;
    
    // PID计算
    ctrl->integral += error;
    
    // 积分限幅 (防止积分饱和)
    float integral_limit = ctrl->output_limit / ctrl->ki;
    ctrl->integral = CLAMP(ctrl->integral, -integral_limit, integral_limit);
    
    float derivative = error - ctrl->last_error;
    
    float output = ctrl->kp * error + 
                   ctrl->ki * ctrl->integral + 
                   ctrl->kd * derivative;
    
    // 输出限制
    output = CLAMP(output, -ctrl->output_limit, ctrl->output_limit);
    
    ctrl->last_error = error;
    return output;
}
```

#### 3.1.2 角度环绕处理
关键技术点：处理-180°到+180°的角度环绕问题
- 目标179°，当前-179°：误差应为-2°而非358°
- 使用条件判断确保误差在±180°范围内

### 3.2 巡线控制算法

#### 3.2.1 权重化线路检测
```c
float LineControl_Update(LineControl_t* ctrl, uint8_t digital, uint8_t* analog)
{
    // 权重化算法计算线路误差
    float weighted_sum = 0;
    float total_active = 0;
    
    for (int i = 0; i < 8; i++) {
        if (digital & (1 << i)) {
            weighted_sum += ctrl->weights[i];
            total_active += 1.0f;
        }
    }
    
    // 归一化误差计算
    float line_error = 0;
    if (total_active > 0) {
        line_error = weighted_sum / total_active;
    } else {
        line_error = ctrl->last_error; // 无线检测时保持上次误差
    }
    
    // PID控制输出
    // ... (PID计算逻辑同角度控制)
}
```

#### 3.2.2 传感器权重配置
```c
// 默认权重配置 (从左到右)
float default_weights[8] = {
    -4.0f,  // 传感器0: 最左侧，强左转
    -2.0f,  // 传感器1: 左侧，中左转
    -1.0f,  // 传感器2: 左内侧，微左转
    -0.5f,  // 传感器3: 左中心，微左转
     0.5f,  // 传感器4: 右中心，微右转
     1.0f,  // 传感器5: 右内侧，微右转
     2.0f,  // 传感器6: 右侧，中右转
     4.0f   // 传感器7: 最右侧，强右转
};
```

### 3.3 电机差速控制算法

#### 3.3.1 差速计算
```c
void MotorControl_Update(MotorControl_t* ctrl)
{
    // 控制信号融合
    float total_correction = ctrl->angle_control + ctrl->line_control;
    
    // 修正幅度限制
    total_correction = CLAMP(total_correction, -ctrl->max_correction, ctrl->max_correction);
    
    // 差速计算
    float left_speed = ctrl->base_speed + total_correction;
    float right_speed = ctrl->base_speed - total_correction;
    
    // 速度限制和输出
    left_speed = CLAMP(left_speed, -100, 100);
    right_speed = CLAMP(right_speed, -100, 100);
    
    // 输出到电机驱动
    Motor_SetSpeed(ctrl->left_motor, left_speed / 100.0f);
    Motor_SetSpeed(ctrl->right_motor, right_speed / 100.0f);
}
```

#### 3.3.2 控制信号融合策略
- **ANGLE_ONLY模式**: 仅使用角度控制输出
- **LINE_ONLY模式**: 仅使用巡线控制输出  
- **FUSION模式**: 角度控制 + 巡线控制的线性叠加

---

## 4. 数据结构设计

### 4.1 核心数据结构

#### 4.1.1 传感器数据结构
```c
typedef struct {
    // JY901S数据
    float yaw_angle;            // Yaw角度 (-180° ~ +180°)
    float roll_angle;           // Roll角度
    float pitch_angle;          // Pitch角度
    uint32_t angle_timestamp;   // 角度数据时间戳
    
    // 灰度传感器数据
    uint8_t gray_digital;       // 8位数字量 (每位代表一个传感器)
    uint8_t gray_analog[8];     // 8路模拟量 (0-255)
    uint32_t gray_timestamp;    // 灰度数据时间戳
    
    // 数据有效性标志
    bool angle_valid;           // 角度数据有效
    bool gray_valid;            // 灰度数据有效
} SensorData_t;
```

#### 4.1.2 控制器数据结构
```c
typedef struct {
    // PID参数
    float kp, ki, kd;           // PID系数
    float integral;             // 积分累积
    float last_error;           // 上次误差
    float output_limit;         // 输出限制
    
    // 控制状态
    float target_angle;         // 目标角度
    float current_angle;        // 当前角度
    float angle_error;          // 角度误差
    float control_output;       // 控制输出
    
    // 配置参数
    uint32_t update_period;     // 更新周期(ms)
    uint32_t last_update;       // 上次更新时间
    bool enabled;               // 使能标志
} AngleControl_t;
```

### 4.2 配置参数结构
```c
typedef struct {
    // 角度控制参数
    float angle_kp, angle_ki, angle_kd;
    float angle_output_limit;
    
    // 巡线控制参数  
    float line_kp, line_ki, line_kd;
    float line_weights[8];
    float line_output_limit;
    
    // 电机控制参数
    uint8_t base_speed;         // 基础速度 (0-100)
    float max_correction;       // 最大修正幅度
    
    // 系统参数
    LineFollowing_Mode_t mode;  // 控制模式
    uint32_t update_period;     // 更新周期(ms)
    bool debug_enabled;         // 调试使能
} LineFollowing_Config_t;
```

---

## 5. API接口文档

### 5.1 初始化接口

#### 5.1.1 系统初始化
```c
HAL_StatusTypeDef LineFollowing_Init(LineFollowing_Config_t* config);
```
**功能**: 初始化循迹系统
**参数**: 
- `config`: 配置参数指针，传NULL使用默认配置
**返回**: HAL_OK成功，HAL_ERROR失败
**使用示例**:
```c
LineFollowing_Config_t config = {
    .angle_kp = 2.0f,
    .angle_ki = 0.1f,
    .angle_kd = 0.5f,
    .base_speed = 50,
    .mode = FUSION
};
LineFollowing_Init(&config);
```

### 5.2 控制接口

#### 5.2.1 系统启动/停止
```c
void LineFollowing_Start(void);
void LineFollowing_Stop(void);
```

#### 5.2.2 参数设置
```c
void LineFollowing_SetTargetAngle(float target_angle);
void LineFollowing_SetMode(LineFollowing_Mode_t mode);
void LineFollowing_SetBaseSpeed(uint8_t speed);
```

#### 5.2.3 主循环更新
```c
void LineFollowing_Update(void);
```
**功能**: 循迹系统主循环更新
**调用频率**: 建议20Hz (50ms周期)
**注意**: 此函数非阻塞，必须在main循环中持续调用

### 5.3 状态查询接口

#### 5.3.1 系统状态查询
```c
LineFollowing_State_t LineFollowing_GetState(void);
void LineFollowing_GetControlData(float* angle_error, float* line_error, 
                                  int8_t* left_speed, int8_t* right_speed);
void LineFollowing_GetDebugData(DebugData_t* debug_data);
```

### 5.4 配置接口

#### 5.4.1 PID参数调整
```c
void LineFollowing_SetAnglePID(float kp, float ki, float kd);
void LineFollowing_SetLinePID(float kp, float ki, float kd);
void LineFollowing_SetSensorWeights(float weights[8]);
```

---

## 6. 集成指南

### 6.1 硬件连接要求

#### 6.1.1 JY901S陀螺仪
- **通信接口**: UART5
- **波特率**: 9600
- **数据格式**: 8N1
- **连接**: TX->PA0, RX->PA1

#### 6.1.2 8路灰度传感器
- **通信接口**: I2C2 (软件I2C)
- **地址**: 0x48 (可配置)
- **连接**: SCL->PB10, SDA->PB11

#### 6.1.3 DRV8871电机驱动
- **控制接口**: TIM1 PWM
- **连接**: 
  - 左电机: IN1->PA8, IN2->PA9
  - 右电机: IN1->PA10, IN2->PA11

### 6.2 软件集成步骤

#### 6.2.1 在main.c中添加头文件
```c
#include "line_following.h"
```

#### 6.2.2 在main()函数中初始化
```c
int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    
    // 外设初始化
    MX_GPIO_Init();
    MX_I2C2_Init();
    MX_UART5_Init();
    MX_TIM1_Init();
    
    // 循迹功能初始化
    LineFollowing_Init(NULL); // 使用默认配置
    
    // 启动循迹
    LineFollowing_SetTargetAngle(0.0f);
    LineFollowing_Start();
    
    while (1)
    {
        // 循迹主循环
        LineFollowing_Update();
        
        // 其他任务
        HAL_Delay(10);
    }
}
```

#### 6.2.3 UART中断处理
```c
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == UART5)
    {
        // JY901S数据处理
        JY901S_ProcessByte(&jy901s_handle, uart_rx_buffer[0]);
        HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
    }
}
```

---

## 7. 调试与优化

### 7.1 调试接口

#### 7.1.1 OLED显示调试
```c
void LineFollowing_DisplayStatus(void);
```
显示内容：
- 第1行：目标角度 | 当前角度
- 第2行：角度误差 | 线路误差  
- 第3行：左电机速度 | 右电机速度
- 第4行：系统状态 | 传感器状态 | 更新频率

#### 7.1.2 调试数据获取
```c
DebugData_t debug_data;
LineFollowing_GetDebugData(&debug_data);
printf("角度误差: %.2f°, 线路误差: %.2f\n", 
       debug_data.angle_error, debug_data.line_error);
```

### 7.2 参数调优指南

#### 7.2.1 角度控制PID调优
1. **比例系数(Kp)**: 控制响应速度
   - 过小：响应慢，稳态误差大
   - 过大：震荡，超调
   - 推荐范围：1.5 ~ 3.0

2. **积分系数(Ki)**: 消除稳态误差
   - 过小：稳态误差大
   - 过大：积分饱和，震荡
   - 推荐范围：0.05 ~ 0.2

3. **微分系数(Kd)**: 抑制震荡
   - 过小：震荡明显
   - 过大：对噪声敏感
   - 推荐范围：0.3 ~ 0.8

#### 7.2.2 巡线控制参数调优
1. **传感器权重**: 根据传感器布局调整
   - 外侧传感器权重更大
   - 中心传感器权重较小
   - 权重符号决定转向方向

2. **PID参数**: 通常比角度控制参数小
   - Kp: 1.0 ~ 2.0
   - Ki: 0.02 ~ 0.1  
   - Kd: 0.2 ~ 0.5

### 7.3 性能优化建议

#### 7.3.1 实时性优化
- 控制循环频率保持20Hz以上
- 避免在控制循环中使用阻塞函数
- 传感器数据采集使用中断方式

#### 7.3.2 稳定性优化
- 添加传感器数据有效性检查
- 实现传感器故障检测和恢复
- 使用滤波算法减少传感器噪声

---

## 8. 故障排除

### 8.1 常见问题及解决方案

#### 8.1.1 系统无响应
**现象**: 调用LineFollowing_Update()无效果
**可能原因**:
- 未调用LineFollowing_Start()
- 传感器数据无效
- 控制器未使能

**解决方案**:
```c
// 检查系统状态
LineFollowing_State_t state = LineFollowing_GetState();
if (state == LF_IDLE) {
    LineFollowing_Start();
}

// 检查传感器数据
DebugData_t debug_data;
LineFollowing_GetDebugData(&debug_data);
if (!debug_data.angle_sensor_ok) {
    // 检查JY901S连接和配置
}
```

#### 8.1.2 角度控制震荡
**现象**: 车辆左右摆动，无法稳定
**可能原因**:
- PID参数不合适
- 传感器噪声过大
- 机械结构问题

**解决方案**:
```c
// 降低PID参数
LineFollowing_SetAnglePID(1.5f, 0.05f, 0.3f);

// 增加输出限制
config.angle_output_limit = 30.0f;
```

#### 8.1.3 巡线跟踪不准确
**现象**: 无法准确跟踪线路
**可能原因**:
- 传感器权重配置不当
- 传感器阈值不合适
- 线路对比度不够

**解决方案**:
```c
// 调整传感器权重
float new_weights[8] = {-3.0f, -1.5f, -0.8f, -0.3f, 0.3f, 0.8f, 1.5f, 3.0f};
LineFollowing_SetSensorWeights(new_weights);

// 调整巡线PID参数
LineFollowing_SetLinePID(1.2f, 0.03f, 0.25f);
```

---

## 9. 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| V1.0 | 2025-07-29 | 初始版本，完整功能实现 | Alex |

---

**文档结束**

> **备注**：本技术文档提供了循迹功能的完整实现细节，包括算法原理、API接口、集成指南和调试方法。开发者可根据实际需求调整参数和配置。
