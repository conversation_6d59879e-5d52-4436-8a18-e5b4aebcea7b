# TestModule 使用指南

## 概述

TestModule是STM32F407项目的测试功能封装模块，将原本分散在main.c中的各种测试函数进行了模块化封装，提供了统一、易用的API接口。

## 功能特性

### ✅ 已封装的测试功能
- **灰度传感器测试** - I2C数字灰度传感器的初始化、数据读取和显示
- **EMMV5电机测试** - 双轴步进电机的初始化、控制和状态显示
- **辅助工具函数** - OLED显示、I2C扫描等通用工具
- **调试接口** - 统计信息、调试模式控制等

### 🎯 设计优势
- **模块化设计** - 独立的.h/.c文件，便于维护
- **统一API** - 简洁一致的接口设计
- **非阻塞执行** - 时间片调度，不影响主循环
- **配置灵活** - 可选择启用/禁用特定测试功能
- **状态管理** - 完整的状态跟踪和错误处理

## 快速开始

### 1. 基本初始化

```c
#include "TestModule.h"

int main(void)
{
    // 系统初始化...
    
    // 使用默认配置初始化TestModule
    if (TestModule_Init(NULL) == HAL_OK) {
        // 初始化成功
    }
    
    while (1) {
        // 主循环中调用更新函数
        TestModule_Update();
        
        // 其他业务逻辑...
    }
}
```

### 2. 自定义配置初始化

```c
TestModule_Config_t config = {
    .grayscale_enabled = true,      // 启用灰度传感器测试
    .motor_test_enabled = false,    // 禁用电机测试
    .emmv5_test_enabled = true,     // 启用EMMV5测试
    .test_interval_ms = 50          // 50ms测试间隔
};

TestModule_Init(&config);
```

## API接口详解

### 核心控制接口

#### `TestModule_Init(config)`
- **功能**: 初始化测试模块
- **参数**: `config` - 配置结构体指针，传NULL使用默认配置
- **返回**: `HAL_OK`成功，`HAL_ERROR`失败
- **说明**: 必须在使用其他API前调用

#### `TestModule_Update()`
- **功能**: 测试模块主循环更新
- **参数**: 无
- **返回**: 无
- **说明**: 非阻塞函数，建议在主循环中高频调用

#### `TestModule_EnableTest(test_type, enable)`
- **功能**: 动态启用/禁用特定测试
- **参数**: 
  - `test_type`: 0=灰度传感器, 1=电机测试, 2=EMMV5测试
  - `enable`: true=启用, false=禁用
- **返回**: 无

### 灰度传感器接口

#### `TestModule_GrayScale_Init()`
- **功能**: 初始化灰度传感器
- **返回**: `HAL_OK`成功，`HAL_ERROR`失败
- **说明**: 自动扫描I2C地址0x4C，检测传感器连接状态

#### `TestModule_GrayScale_ShowData(line)`
- **功能**: 在OLED指定行显示灰度数据
- **参数**: `line` - OLED显示行号(0-7)
- **显示格式**: `G:1010 1100 A:123` (数字量+模拟量平均值)

#### `TestModule_GrayScale_IsConnected()`
- **功能**: 获取灰度传感器连接状态
- **返回**: true=已连接, false=未连接

### EMMV5电机接口

#### `TestModule_EMMV5_Init()`
- **功能**: 初始化EMMV5双轴电机
- **返回**: `HAL_OK`成功，`HAL_ERROR`失败
- **说明**: 自动配置X轴(UART2)和Y轴(UART4)电机

#### `TestModule_EMMV5_ShowStatus(x, y)`
- **功能**: 在OLED指定位置显示电机状态
- **参数**: `x`, `y` - OLED显示坐标
- **显示格式**: `X:+ Y:S` (X轴正转，Y轴停止)

#### `TestModule_EMMV5_StopAll()`
- **功能**: 立即停止所有EMMV5电机
- **说明**: 紧急停止功能，可随时调用

### 辅助工具接口

#### `TestModule_OLED_ShowSignedAngle(x, y, angle)`
- **功能**: 显示带符号的角度值
- **参数**: 
  - `x`, `y` - OLED坐标
  - `angle` - 角度值(-180~+180)
- **显示格式**: `+123` 或 `-045`

#### `TestModule_I2C_ScanAddress(addr)`
- **功能**: 扫描单个I2C地址
- **参数**: `addr` - I2C地址(7位)
- **返回**: 0=有应答, 1=无应答

#### `TestModule_I2C_ScanAllDevices(found_devices, max_devices)`
- **功能**: 扫描所有I2C设备
- **参数**: 
  - `found_devices` - 存储找到设备地址的数组
  - `max_devices` - 数组最大长度
- **返回**: 找到的设备数量

### 调试接口

#### `TestModule_SetDebugMode(enable)`
- **功能**: 启用/禁用调试模式
- **参数**: `enable` - true=启用, false=禁用

#### `TestModule_GetStatus()`
- **功能**: 获取测试状态结构体
- **返回**: `TestModule_Status_t*` 状态指针
- **用途**: 获取连接状态、计数器等信息

#### `TestModule_GetStatsString(buffer, size)`
- **功能**: 获取统计信息字符串
- **参数**: 
  - `buffer` - 字符串缓冲区
  - `size` - 缓冲区大小
- **格式**: `"Tests:1234 EMMV5:OK Gray:NO"`

## 使用示例

### 示例1: 灰度传感器测试

```c
// 初始化时启用灰度传感器
TestModule_Config_t config = {
    .grayscale_enabled = true,
    .test_interval_ms = 100
};
TestModule_Init(&config);

// 主循环
while (1) {
    TestModule_Update();
    
    // 每秒显示一次灰度数据
    static uint32_t last_display = 0;
    if (HAL_GetTick() - last_display > 1000) {
        if (TestModule_GrayScale_IsConnected()) {
            TestModule_GrayScale_ShowData(3);  // 在第3行显示
        }
        last_display = HAL_GetTick();
    }
}
```

### 示例2: EMMV5电机测试

```c
// 启用EMMV5测试
TestModule_EnableTest(2, true);  // 2=EMMV5测试

// 显示电机状态
TestModule_EMMV5_ShowStatus(88, 3);  // 在(88,3)位置显示

// 紧急停止
if (emergency_stop) {
    TestModule_EMMV5_StopAll();
}
```

### 示例3: I2C设备扫描

```c
uint8_t found_devices[10];
uint8_t device_count = TestModule_I2C_ScanAllDevices(found_devices, 10);

OLED_ShowStr(0, 0, "I2C Devices:", 8);
for (int i = 0; i < device_count; i++) {
    OLED_ShowNum(i * 24, 1, found_devices[i], 2, 8);
}
```

## 项目集成

### 1. 添加到Keil项目

1. 将 `TestModule.c` 添加到项目的源文件组
2. 将 `Moudle/TestModule` 添加到包含路径
3. 在main.c中包含头文件: `#include "TestModule.h"`

### 2. 依赖关系

TestModule依赖以下模块：
- `oled.h` - OLED显示
- `software_iic.h` - I2C通信
- `Emm_V5.h` - EMMV5电机控制
- `main.h` - HAL库

### 3. 硬件连接

- **灰度传感器**: I2C接口，地址0x4C
- **EMMV5 X轴电机**: UART2
- **EMMV5 Y轴电机**: UART4
- **OLED显示屏**: I2C接口

## 注意事项

1. **初始化顺序**: 必须先调用`TestModule_Init()`再使用其他API
2. **非阻塞设计**: 所有函数都是非阻塞的，适合在主循环中调用
3. **资源管理**: 模块会自动管理内部资源，无需手动释放
4. **错误处理**: 所有初始化函数都有返回值，建议检查
5. **线程安全**: 模块不是线程安全的，仅适用于单线程环境

## 故障排除

### 常见问题

1. **编译错误**: 检查是否正确添加了包含路径和源文件
2. **灰度传感器无法连接**: 检查I2C接线和地址设置
3. **EMMV5电机无响应**: 检查UART接线和波特率配置
4. **OLED显示异常**: 检查I2C接线和OLED初始化

### 调试建议

1. 启用调试模式: `TestModule_SetDebugMode(true)`
2. 检查状态信息: `TestModule_GetStatus()`
3. 使用I2C扫描: `TestModule_I2C_ScanAllDevices()`
4. 查看统计信息: `TestModule_GetStatsString()`

---

**版本**: V1.0  
**作者**: Alex (工程师)  
**日期**: 2025-07-29  
**项目**: STM32F407 K230D追踪系统
