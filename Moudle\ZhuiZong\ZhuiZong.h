#ifndef __ZHUIZONG_H
#define __ZHUIZONG_H

#include "main.h"
#include "stdbool.h"
#include "K230D_getData.h"
#include "Emm_V5.h"

/**********************************************************
***	K230D红色追踪系统模块
***	编写作者：Alex (工程师)
***	功能：轻量级非阻塞红色目标追踪
***	版本：V3.0 - EMMV5云台控制版
***	日期：2025-07-29
**********************************************************/

// 追踪状态枚举
typedef enum {
    TRACK_IDLE = 0,     // 空闲状态
    TRACK_SEARCH,       // 搜索状态
    TRACK_ACTIVE,       // 追踪状态
    TRACK_LOST          // 丢失状态
} TrackingState_t;

// 轻量级追踪器结构体
typedef struct {
    int16_t target_x, target_y;    // 目标坐标
    uint32_t last_update;          // 最后更新时间
    bool valid;                    // 数据有效标志
    TrackingState_t state;         // 当前状态
    uint32_t state_timer;          // 状态计时器
    uint16_t valid_count;          // 有效计数
    uint16_t lost_count;           // 丢失计数
} LightweightTracker_t;

// 追踪配置参数结构体
typedef struct {
    uint16_t image_center_x;       // 图像中心X坐标 (默认320)
    uint16_t image_center_y;       // 图像中心Y坐标 (默认240)
    uint16_t dead_zone_x;          // X轴死区 (默认20像素)
    uint16_t dead_zone_y;          // Y轴死区 (默认20像素)
    uint8_t control_gain;          // 控制增益 (默认3, 即1/3比例)
    int16_t max_speed_x;           // X轴最大速度 (默认10)
    int16_t max_speed_y;           // Y轴最大速度 (默认5)
    uint16_t timeout_ms;           // 数据超时时间 (默认200ms)
    uint8_t x_motor_addr;          // X轴电机地址 (默认1)
    uint8_t y_motor_addr;          // Y轴电机地址 (默认1)
    UART_HandleTypeDef* x_uart;   // X轴UART句柄
    UART_HandleTypeDef* y_uart;   // Y轴UART句柄
} ZhuiZong_Config_t;

// 追踪统计信息结构体
typedef struct {
    uint32_t total_updates;        // 总更新次数
    uint32_t valid_targets;        // 有效目标次数
    uint32_t lost_targets;         // 丢失目标次数
    uint32_t control_commands;     // 控制命令次数
    uint32_t last_control_time;    // 最后控制时间
    int16_t last_error_x;          // 最后X轴误差
    int16_t last_error_y;          // 最后Y轴误差
    int16_t last_speed_x;          // 最后X轴速度
    int16_t last_speed_y;          // 最后Y轴速度
} ZhuiZong_Stats_t;

/**********************************************************
*** 核心API接口 - 简单易用
**********************************************************/

/**
  * @brief 初始化追踪系统
  * @param config: 追踪配置参数 (可传NULL使用默认配置)
  * @retval HAL_OK: 初始化成功, HAL_ERROR: 初始化失败
  */
HAL_StatusTypeDef ZhuiZong_Init(ZhuiZong_Config_t* config);

/**
  * @brief 追踪系统主循环更新 (在main循环中调用)
  * @retval None
  * @note 此函数非阻塞，可高频率调用
  */
void ZhuiZong_Update(void);

/**
  * @brief 启动追踪
  * @retval None
  */
void ZhuiZong_Start(void);

/**
  * @brief 停止追踪
  * @retval None
  */
void ZhuiZong_Stop(void);

/**
  * @brief 暂停追踪 (保持状态，停止电机)
  * @retval None
  */
void ZhuiZong_Pause(void);

/**
  * @brief 恢复追踪
  * @retval None
  */
void ZhuiZong_Resume(void);

/**********************************************************
*** 状态查询接口
**********************************************************/

/**
  * @brief 获取当前追踪状态
  * @retval TrackingState_t: 当前状态
  */
TrackingState_t ZhuiZong_GetState(void);

/**
  * @brief 获取当前目标坐标
  * @param x: X坐标指针
  * @param y: Y坐标指针
  * @retval bool: true=有效坐标, false=无效坐标
  */
bool ZhuiZong_GetTargetPos(int16_t* x, int16_t* y);

/**
  * @brief 获取控制误差
  * @param error_x: X轴误差指针
  * @param error_y: Y轴误差指针
  * @retval None
  */
void ZhuiZong_GetError(int16_t* error_x, int16_t* error_y);

/**
  * @brief 获取统计信息
  * @retval ZhuiZong_Stats_t*: 统计信息指针
  */
ZhuiZong_Stats_t* ZhuiZong_GetStats(void);

/**
  * @brief 获取状态字符串 (用于显示)
  * @retval const char*: 状态字符串
  */
const char* ZhuiZong_GetStateString(void);

/**********************************************************
*** 配置接口
**********************************************************/

/**
  * @brief 设置图像中心坐标
  * @param center_x: 中心X坐标
  * @param center_y: 中心Y坐标
  * @retval None
  */
void ZhuiZong_SetImageCenter(uint16_t center_x, uint16_t center_y);

/**
  * @brief 设置死区大小
  * @param dead_zone_x: X轴死区
  * @param dead_zone_y: Y轴死区
  * @retval None
  */
void ZhuiZong_SetDeadZone(uint16_t dead_zone_x, uint16_t dead_zone_y);

/**
  * @brief 设置最大速度
  * @param max_speed_x: X轴最大速度
  * @param max_speed_y: Y轴最大速度
  * @retval None
  */
void ZhuiZong_SetMaxSpeed(int16_t max_speed_x, int16_t max_speed_y);

/**
  * @brief 设置控制增益
  * @param gain: 控制增益 (1-10)
  * @retval None
  */
void ZhuiZong_SetControlGain(uint8_t gain);

/**********************************************************
*** 调试接口
**********************************************************/

/**
  * @brief 启用/禁用调试模式
  * @param enable: true=启用, false=禁用
  * @retval None
  */
void ZhuiZong_SetDebugMode(bool enable);

/**
  * @brief 手动设置目标坐标 (调试用)
  * @param x: X坐标
  * @param y: Y坐标
  * @retval None
  */
void ZhuiZong_SetManualTarget(int16_t x, int16_t y);

/**
  * @brief 重置统计信息
  * @retval None
  */
void ZhuiZong_ResetStats(void);

/**********************************************************
*** 内部函数声明 (用户无需调用)
**********************************************************/
void ZhuiZong_ProcessData(void);
void ZhuiZong_UpdateControl(void);
void ZhuiZong_SendMotorCommand(int16_t speed_x, int16_t speed_y);
void ZhuiZong_CheckTimeout(void);

#endif // __ZHUIZONG_H
