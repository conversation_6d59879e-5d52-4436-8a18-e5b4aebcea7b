#include "EMMV5_PID.h"
#include <string.h>
#include <stdio.h>
#include "../EMM_V5/Emm_V5.h"

/**********************************************************
*** EMMV5 PID云台追踪系统实现
*** 编写作者：米醋电子工作室技术团队
*** 版本：V1.0
*** 日期：2025-07-28
*** 功能：接收K230D红色追踪坐标，PID控制EMMV5云台追踪
**********************************************************/

/* 静态系统实例 (避免全局变量内存冲突) */
static EMMV5_PID_System_t s_emmv5_pid_system;

/**
 * @brief 获取EMMV5_PID系统实例指针
 * @return 系统实例指针
 */
EMMV5_PID_System_t* EMMV5_PID_GetSystemInstance(void)
{
    return &s_emmv5_pid_system;
}

/**
 * @brief 系统初始化
 * @param system: 系统结构体指针
 * @param k230d_uart: K230D通信串口
 * @param x_motor_uart: X轴电机串口
 * @param y_motor_uart: Y轴电机串口
 */
void EMMV5_PID_Init(EMMV5_PID_System_t *system, 
                    UART_HandleTypeDef *k230d_uart,
                    UART_HandleTypeDef *x_motor_uart, 
                    UART_HandleTypeDef *y_motor_uart)
{
    /* 清零系统结构体 */
    memset(system, 0, sizeof(EMMV5_PID_System_t));
    
    /* 配置硬件接口 */
    system->k230d_uart = k230d_uart;
    system->x_motor_uart = x_motor_uart;
    system->y_motor_uart = y_motor_uart;
    
    /* 初始化PID控制器 */
    PID_Init(&system->x_pid, PID_KP_DEFAULT, PID_KI_DEFAULT, PID_KD_DEFAULT);
    PID_Init(&system->y_pid, PID_KP_DEFAULT, PID_KI_DEFAULT, PID_KD_DEFAULT);
    
    /* 设置PID目标值为图像中心 */
    system->x_pid.setpoint = IMAGE_CENTER_X;
    system->y_pid.setpoint = IMAGE_CENTER_Y;
    
    /* 初始化系统参数 */
    system->auto_tracking = true;
    system->debug_mode = false;
    system->control_period = 50;        /* 20Hz控制频率 */
    system->timeout_threshold = 1000;   /* 1秒超时 */
    system->parse_state = PARSE_IDLE;
    
    /* 初始化云台状态 */
    system->gimbal.tracking_active = false;
    system->gimbal.x_enabled = false;
    system->gimbal.y_enabled = false;
    
    /* 启动UART接收中断 (仅当UART有效时) */
    if (system->k230d_uart != NULL) {
        HAL_UART_Receive_IT(system->k230d_uart, system->rx_buffer, 1);
        printf("EMMV5 PID System Initialized with K230D UART\r\n");
    } else {
        printf("EMMV5 PID System Initialized (K230D UART disabled)\r\n");
    }
    printf("Control Period: %dms, Timeout: %dms\r\n", 
           system->control_period, system->timeout_threshold);
}

/**
 * @brief 配置PID参数
 */
void EMMV5_PID_Config(EMMV5_PID_System_t *system, 
                      float x_kp, float x_ki, float x_kd,
                      float y_kp, float y_ki, float y_kd)
{
    PID_SetTunings(&system->x_pid, x_kp, x_ki, x_kd);
    PID_SetTunings(&system->y_pid, y_kp, y_ki, y_kd);
    
    printf("PID Config - X: Kp=%.2f Ki=%.2f Kd=%.2f\r\n", x_kp, x_ki, x_kd);
    printf("PID Config - Y: Kp=%.2f Ki=%.2f Kd=%.2f\r\n", y_kp, y_ki, y_kd);
}

/**
 * @brief PID控制器初始化
 */
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
    pid->setpoint = 0.0f;
    pid->input = 0.0f;
    pid->output = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->error = 0.0f;
    pid->last_time = 0;
    pid->first_run = true;
}

/**
 * @brief PID计算
 */
float PID_Compute(PID_Controller_t *pid, float setpoint, float input)
{
    uint32_t now = HAL_GetTick();
    float dt;
    
    pid->setpoint = setpoint;
    pid->input = input;
    pid->error = setpoint - input;
    
    if (pid->first_run) {
        pid->last_time = now;
        pid->last_error = pid->error;
        pid->first_run = false;
        return 0.0f;
    }
    
    dt = (float)(now - pid->last_time) / 1000.0f;  /* 转换为秒 */
    
    if (dt <= 0.0f) return pid->output;
    
    /* 比例项 */
    float proportional = pid->kp * pid->error;
    
    /* 积分项 */
    pid->integral += pid->error * dt;
    pid->integral = EMMV5_PID_ConstrainFloat(pid->integral, 
                                            PID_INTEGRAL_MIN, PID_INTEGRAL_MAX);
    float integral = pid->ki * pid->integral;
    
    /* 微分项 */
    pid->derivative = (pid->error - pid->last_error) / dt;
    float differential = pid->kd * pid->derivative;
    
    /* PID输出 */
    pid->output = proportional + integral + differential;
    pid->output = EMMV5_PID_ConstrainFloat(pid->output, 
                                          PID_OUTPUT_MIN, PID_OUTPUT_MAX);
    
    /* 更新历史值 */
    pid->last_error = pid->error;
    pid->last_time = now;
    
    return pid->output;
}

/**
 * @brief UART接收回调函数
 */
void EMMV5_PID_UART_RxCallback(EMMV5_PID_System_t *system, uint8_t *data, uint16_t size)
{
    for (uint16_t i = 0; i < size; i++) {
        uint8_t byte = data[i];
        
        /* 防止缓冲区溢出 */
        if (system->rx_index >= EMMV5_PID_BUFFER_SIZE - 1) {
            system->rx_index = 0;
            system->parse_state = PARSE_IDLE;
        }
        
        system->rx_buffer[system->rx_index++] = byte;
        
        /* 检测到换行符，开始解析 */
        if (byte == '\n') {
            system->rx_buffer[system->rx_index - 1] = '\0';  /* 替换换行符为结束符 */
            EMMV5_PID_ProcessReceivedData(system);
            system->rx_index = 0;
            system->parse_state = PARSE_IDLE;
        }
    }
    
    /* 重新启动接收 */
    HAL_UART_Receive_IT(system->k230d_uart, system->rx_buffer + system->rx_index, 1);
}

/**
 * @brief 处理接收到的数据
 */
void EMMV5_PID_ProcessReceivedData(EMMV5_PID_System_t *system)
{
    char *data_str = (char *)system->rx_buffer;
    
    system->stats.total_packets++;
    
    /* 解析坐标数据 */
    if (EMMV5_PID_ParseCoordinate(system, data_str)) {
        system->stats.valid_packets++;
        system->stats.last_packet_time = HAL_GetTick();
        
        /* 更新目标坐标 */
        system->target.timestamp = HAL_GetTick();
        system->target.valid = true;
        
        if (system->debug_mode) {
            printf("Target: X=%d, Y=%d\r\n", system->target.x, system->target.y);
        }
    } else {
        system->stats.error_packets++;
        if (system->debug_mode) {
            printf("Parse Error: %s\r\n", data_str);
        }
    }
}

/**
 * @brief 解析坐标数据
 * 数据格式: "RED_CENTER:X=320,Y=240"
 */
bool EMMV5_PID_ParseCoordinate(EMMV5_PID_System_t *system, char *data_str)
{
    char *x_pos, *y_pos;
    int x_val, y_val;
    
    /* 检查数据头 */
    if (strncmp(data_str, "RED_CENTER:", 11) != 0) {
        return false;
    }
    
    /* 查找X坐标 */
    x_pos = strstr(data_str, "X=");
    if (x_pos == NULL) return false;
    x_val = atoi(x_pos + 2);
    
    /* 查找Y坐标 */
    y_pos = strstr(data_str, "Y=");
    if (y_pos == NULL) return false;
    y_val = atoi(y_pos + 2);
    
    /* 坐标范围检查 */
    if (x_val < 0 || x_val > 640 || y_val < 0 || y_val > 480) {
        return false;
    }
    
    /* 更新目标坐标 */
    system->target.x = (int16_t)x_val;
    system->target.y = (int16_t)y_val;
    
    return true;
}

/**
 * @brief 使能电机
 */
void EMMV5_PID_EnableMotors(EMMV5_PID_System_t *system, bool enable)
{
    /* 使能X轴电机 */
    Emm_V5_En_Control(system->x_motor_uart, EMMV5_X_MOTOR_ADDR, enable, false);
    HAL_Delay(10);
    
    /* 使能Y轴电机 */
    Emm_V5_En_Control(system->y_motor_uart, EMMV5_Y_MOTOR_ADDR, enable, false);
    HAL_Delay(10);
    
    system->gimbal.x_enabled = enable;
    system->gimbal.y_enabled = enable;
    
    printf("Motors %s\r\n", enable ? "Enabled" : "Disabled");
}

/**
 * @brief 更新控制输出
 */
void EMMV5_PID_UpdateControl(EMMV5_PID_System_t *system)
{
    uint32_t current_time = HAL_GetTick();
    
    /* 检查数据超时 */
    if (system->target.valid && 
        (current_time - system->target.timestamp) > system->timeout_threshold) {
        system->target.valid = false;
        system->gimbal.tracking_active = false;
        EMMV5_PID_StopMotors(system);
        
        if (system->debug_mode) {
            printf("Target timeout, stopping motors\r\n");
        }
        return;
    }
    
    /* 如果没有有效目标或未启用自动追踪，退出 */
    if (!system->target.valid || !system->auto_tracking) {
        return;
    }
    
    /* 计算PID输出 */
    float x_output = PID_Compute(&system->x_pid, IMAGE_CENTER_X, system->target.x);
    float y_output = PID_Compute(&system->y_pid, IMAGE_CENTER_Y, system->target.y);
    
    /* 转换为电机速度 */
    int16_t x_speed = (int16_t)EMMV5_PID_Constrain((int16_t)x_output, 
                                                   -MOTOR_MAX_SPEED, MOTOR_MAX_SPEED);
    int16_t y_speed = (int16_t)EMMV5_PID_Constrain((int16_t)y_output, 
                                                   -MOTOR_MAX_SPEED, MOTOR_MAX_SPEED);
    
    /* 死区处理 */
    if (abs(x_speed) < MOTOR_DEAD_ZONE) x_speed = 0;
    if (abs(y_speed) < MOTOR_DEAD_ZONE) y_speed = 0;
    
    /* 控制电机 */
    if (x_speed != 0) {
        EMMV5_PID_MoveMotor(system->x_motor_uart, EMMV5_X_MOTOR_ADDR, 
                           x_speed, MOTOR_ACCELERATION);
    }
    
    if (y_speed != 0) {
        EMMV5_PID_MoveMotor(system->y_motor_uart, EMMV5_Y_MOTOR_ADDR, 
                           y_speed, MOTOR_ACCELERATION);
    }
    
    /* 更新状态 */
    system->gimbal.x_speed = x_speed;
    system->gimbal.y_speed = y_speed;
    system->gimbal.tracking_active = (x_speed != 0 || y_speed != 0);
    system->gimbal.last_update = current_time;
    
    if (system->debug_mode && (x_speed != 0 || y_speed != 0)) {
        printf("Control: X_err=%.1f X_out=%.1f X_spd=%d | Y_err=%.1f Y_out=%.1f Y_spd=%d\r\n",
               system->x_pid.error, x_output, x_speed,
               system->y_pid.error, y_output, y_speed);
    }
}

/**
 * @brief 控制电机运动
 */
void EMMV5_PID_MoveMotor(UART_HandleTypeDef *huart, uint8_t addr, 
                         int16_t speed, uint8_t acceleration)
{
    uint8_t dir = (speed >= 0) ? 0 : 1;  /* 0=CW, 1=CCW */
    uint16_t abs_speed = (uint16_t)abs(speed);
    
    /* 限制速度范围 */
    if (abs_speed < MOTOR_MIN_SPEED && abs_speed > 0) {
        abs_speed = MOTOR_MIN_SPEED;
    }
    
    if (abs_speed > 0) {
        Emm_V5_Vel_Control(huart, addr, dir, abs_speed, acceleration, false);
    } else {
        Emm_V5_Stop_Now(huart, addr, false);
    }
}

/**
 * @brief 停止所有电机
 */
void EMMV5_PID_StopMotors(EMMV5_PID_System_t *system)
{
    Emm_V5_Stop_Now(system->x_motor_uart, EMMV5_X_MOTOR_ADDR, false);
    HAL_Delay(5);
    Emm_V5_Stop_Now(system->y_motor_uart, EMMV5_Y_MOTOR_ADDR, false);
    
    system->gimbal.x_speed = 0;
    system->gimbal.y_speed = 0;
    system->gimbal.tracking_active = false;
}

/**
 * @brief 主任务循环
 */
void EMMV5_PID_MainTask(EMMV5_PID_System_t *system)
{
    static uint32_t last_control_time = 0;
    static uint32_t last_stats_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    /* 控制任务 */
    if (current_time - last_control_time >= system->control_period) {
        EMMV5_PID_UpdateControl(system);
        last_control_time = current_time;
    }
    
    /* 统计任务 */
    if (current_time - last_stats_time >= 1000) {  /* 每秒更新一次统计 */
        EMMV5_PID_UpdateStats(system);
        last_stats_time = current_time;
    }
}

/**
 * @brief 更新统计信息
 */
void EMMV5_PID_UpdateStats(EMMV5_PID_System_t *system)
{
    uint32_t current_time = HAL_GetTick();
    
    if (system->stats.last_packet_time > 0) {
        system->stats.average_fps = 1000.0f / 
            (current_time - system->stats.last_packet_time + 1);
    }
    
    if (system->debug_mode) {
        EMMV5_PID_PrintStatus(system);
    }
}

/**
 * @brief 打印系统状态
 */
void EMMV5_PID_PrintStatus(EMMV5_PID_System_t *system)
{
    printf("=== EMMV5 PID Status ===\r\n");
    printf("Target: X=%d Y=%d Valid=%d\r\n", 
           system->target.x, system->target.y, system->target.valid);
    printf("Gimbal: X_spd=%d Y_spd=%d Active=%d\r\n",
           system->gimbal.x_speed, system->gimbal.y_speed, 
           system->gimbal.tracking_active);
    printf("Stats: Total=%d Valid=%d Error=%d FPS=%.1f\r\n",
           system->stats.total_packets, system->stats.valid_packets,
           system->stats.error_packets, system->stats.average_fps);
    printf("========================\r\n");
}

/**
 * @brief 约束函数
 */
int16_t EMMV5_PID_Constrain(int16_t value, int16_t min, int16_t max)
{
    if (value < min) return min;
    if (value > max) return max;
    return value;
}

float EMMV5_PID_ConstrainFloat(float value, float min, float max)
{
    if (value < min) return min;
    if (value > max) return max;
    return value;
}

/**
 * @brief PID参数设置
 */
void PID_SetTunings(PID_Controller_t *pid, float kp, float ki, float kd)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
}

/**
 * @brief 设置追踪目标坐标
 */
void EMMV5_PID_SetTarget(EMMV5_PID_System_t *system, int16_t x, int16_t y)
{
    if (system == NULL) return;

    // 更新目标坐标
    system->target.x = x;
    system->target.y = y;
    system->target.valid = true;
    system->target.timestamp = HAL_GetTick();

    // 重置PID积分项以避免积分饱和
    system->x_pid.integral *= 0.8f;  // 缓慢衰减而不是完全重置
    system->y_pid.integral *= 0.8f;
}

/**
 * @brief PID重置
 */
void PID_Reset(PID_Controller_t *pid)
{
    pid->integral = 0.0f;
    pid->last_error = 0.0f;
    pid->derivative = 0.0f;
    pid->first_run = true;
}
