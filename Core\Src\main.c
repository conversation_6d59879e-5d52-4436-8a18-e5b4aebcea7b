/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : STM32F407智能小车循迹系统测试程序
  * <AUTHOR> 米醋电子工作室技术团队 - Alex (工程师)
  * @version        : V2.0 - 循迹功能完整版
  * @date           : 2025-07-29 【循迹功能集成版】
  ******************************************************************************
  * @attention
  *
  * 功能描述：
  * 1. 基于JY901S陀螺仪的高精度角度控制 (±2°精度)
  * 2. 基于8路灰度传感器的智能巡线控制 (<50ms响应)
  * 3. 三种控制模式：纯角度/纯巡线/融合控制
  * 4. 实时OLED状态显示和串口调试输出
  * 5. 20Hz控制频率，确保实时性能
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "i2c.h"
#include "dma.h"
#include "usart.h"
#include "gpio.h"
#include "tim.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
#include "../../Moudle/oled/oled.h"
#include "../../Moudle/LineFollowing/line_following.h"
#include "../../Moudle/jy901s/jy901s.h"
#include "../../Moudle/grayscale/software_iic.h"
#include "../../Moudle/motor/motor_driver.h"

/* printf重定向到UART6 */
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart6, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
I2C_HandleTypeDef hi2c1;
I2C_HandleTypeDef hi2c2;
TIM_HandleTypeDef htim1;
UART_HandleTypeDef huart2;
UART_HandleTypeDef huart4;
UART_HandleTypeDef huart5;
UART_HandleTypeDef huart6;

/* USER CODE BEGIN PV */

/* 循迹系统全局变量 */
JY901S_Handle jy901s_handle;            // JY901S陀螺仪句柄
JY901S_Data sensor_data;                // JY901S传感器数据
uint8_t uart_rx_buffer[1];              // UART接收缓冲区

/* 灰度传感器变量 */
uint8_t gray_digital_data = 0;          // 8位数字量数据
uint8_t gray_analog_data[8] = {0};      // 8路模拟量数据
uint8_t gray_sensor_connected = 0;      // 传感器连接状态

/* 电机控制变量 */
Motor_Handle motor_left;                // 左电机句柄
Motor_Handle motor_right;               // 右电机句柄

/* 系统状态变量 */
uint32_t system_start_time = 0;         // 系统启动时间
uint32_t last_display_update = 0;       // 上次显示更新时间
uint32_t control_update_count = 0;      // 控制更新计数
bool system_initialized = false;        // 系统初始化标志

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_DMA_Init(void);
static void MX_I2C1_Init(void);
static void MX_I2C2_Init(void);
static void MX_TIM1_Init(void);
static void MX_UART4_Init(void);
static void MX_UART5_Init(void);
static void MX_USART2_UART_Init(void);
static void MX_USART6_UART_Init(void);

/* USER CODE BEGIN PFP */

/* 循迹系统初始化和控制函数 */
void LineFollowing_System_Init(void);
void LineFollowing_System_Test(void);
void LineFollowing_Main_Loop(void);
void System_Status_Display(void);
void System_Debug_Output(void);

/* 硬件模块初始化函数 */
void Hardware_Modules_Init(void);
HAL_StatusTypeDef JY901S_Module_Init(void);
HAL_StatusTypeDef GrayScale_Module_Init(void);
HAL_StatusTypeDef Motor_Module_Init(void);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief 循迹系统完整初始化
 */
void LineFollowing_System_Init(void)
{
    printf("\r\n=== STM32F407智能小车循迹系统初始化 ===\r\n");
    printf("版本: V2.0 - 循迹功能完整版\r\n");
    printf("作者: 米醋电子工作室 - Alex (工程师)\r\n");
    printf("功能: JY901S陀螺仪 + 8路灰度传感器智能循迹\r\n");
    printf("=====================================\r\n");

    // 1. OLED显示器初始化 (最先初始化，用于显示状态)
    printf("1. OLED显示器初始化...\r\n");
    OLED_Init();
    OLED_Clear();
    OLED_ShowStr(0, 0, "LineFollow Init", 16);
    OLED_ShowStr(0, 2, "Please Wait...", 12);

    // 2. 硬件模块初始化
    printf("2. 硬件模块初始化...\r\n");
    Hardware_Modules_Init();

    // 3. 循迹功能初始化
    printf("3. 循迹功能初始化...\r\n");

    // 使用优化的配置参数
    LineFollowing_Config_t config = {
        // 角度控制参数 (针对JY901S优化)
        .angle_kp = 2.5f,
        .angle_ki = 0.1f,
        .angle_kd = 0.8f,
        .angle_output_limit = 40.0f,

        // 巡线控制参数 (针对8路灰度传感器优化)
        .line_kp = 1.8f,
        .line_ki = 0.05f,
        .line_kd = 0.4f,
        .line_output_limit = 35.0f,

        // 传感器权重配置 (从左到右，外侧权重更大)
        .line_weights = {-4.0f, -2.5f, -1.2f, -0.6f, 0.6f, 1.2f, 2.5f, 4.0f},

        // 电机参数
        .base_speed = 45,       // 45%基础速度
        .max_correction = 25,   // 最大25%修正

        // 系统参数
        .mode = FUSION,         // 融合控制模式
        .update_period = 50,    // 50ms更新周期 (20Hz)
        .debug_enabled = true
    };

    if (LineFollowing_Init(&config) == HAL_OK) {
        printf("✅ 循迹系统初始化成功!\r\n");
        OLED_Clear();
        OLED_ShowStr(0, 0, "System Ready!", 16);
        OLED_ShowStr(0, 2, "Mode: FUSION", 12);
        OLED_ShowStr(0, 4, "Speed: 45%", 12);
        system_initialized = true;
    } else {
        printf("❌ 循迹系统初始化失败!\r\n");
        OLED_Clear();
        OLED_ShowStr(0, 0, "Init Failed!", 16);
        OLED_ShowStr(0, 2, "Check Hardware", 12);
        while(1); // 初始化失败，停止运行
    }

    system_start_time = HAL_GetTick();
    printf("系统初始化完成，准备开始循迹控制...\r\n\r\n");
}

/**
 * @brief 硬件模块初始化
 */
void Hardware_Modules_Init(void)
{
    // JY901S陀螺仪初始化
    if (JY901S_Module_Init() == HAL_OK) {
        printf("  ✅ JY901S陀螺仪初始化成功\r\n");
    } else {
        printf("  ❌ JY901S陀螺仪初始化失败\r\n");
    }

    // 灰度传感器初始化
    if (GrayScale_Module_Init() == HAL_OK) {
        printf("  ✅ 8路灰度传感器初始化成功\r\n");
    } else {
        printf("  ❌ 8路灰度传感器初始化失败\r\n");
    }

    // 电机驱动初始化
    if (Motor_Module_Init() == HAL_OK) {
        printf("  ✅ DRV8871电机驱动初始化成功\r\n");
    } else {
        printf("  ❌ DRV8871电机驱动初始化失败\r\n");
    }
}

/**
 * @brief JY901S模块初始化
 */
HAL_StatusTypeDef JY901S_Module_Init(void)
{
    // 初始化JY901S句柄
    JY901S_Init(&jy901s_handle, &huart5, NULL);

    // 启动UART接收中断
    HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);

    // 等待传感器稳定
    HAL_Delay(1000);

    // 检查数据接收
    if (JY901S_GetData(&jy901s_handle, &sensor_data) == HAL_OK) {
        return HAL_OK;
    }

    return HAL_ERROR;
}

/**
 * @brief 灰度传感器模块初始化
 */
HAL_StatusTypeDef GrayScale_Module_Init(void)
{
    // 初始化软件I2C
    GrayScale_Init();

    // 等待传感器稳定
    HAL_Delay(500);

    // 测试数据读取
    if (GrayScale_ReadData() == HAL_OK) {
        gray_sensor_connected = 1;
        return HAL_OK;
    }

    return HAL_ERROR;
}

/**
 * @brief 电机模块初始化
 */
HAL_StatusTypeDef Motor_Module_Init(void)
{
    // 初始化电机驱动
    Motor_Init();

    // 初始化电机句柄
    motor_left.htim = &htim1;
    motor_left.channel_1 = TIM_CHANNEL_1;  // PA8
    motor_left.channel_2 = TIM_CHANNEL_2;  // PA9
    motor_left.speed = 0;
    motor_left.direction = MOTOR_FORWARD;

    motor_right.htim = &htim1;
    motor_right.channel_1 = TIM_CHANNEL_3; // PA10
    motor_right.channel_2 = TIM_CHANNEL_4; // PA11
    motor_right.speed = 0;
    motor_right.direction = MOTOR_FORWARD;

    // 测试电机控制
    Motor_SetSpeed(&motor_left, 0);
    Motor_SetSpeed(&motor_right, 0);

    return HAL_OK;
}

/**
 * @brief 循迹系统主循环
 */
void LineFollowing_Main_Loop(void)
{
    if (!system_initialized) {
        return;
    }

    uint32_t current_time = HAL_GetTick();

    // 1. 循迹控制更新 (高频率调用 - 20Hz)
    LineFollowing_Update();
    control_update_count++;

    // 2. 延时启动控制 (给传感器预热时间)
    static bool control_started = false;
    if (!control_started && current_time - system_start_time > 3000) {
        printf("🚀 启动循迹控制...\r\n");

        // 设置目标角度为0度 (直线行驶)
        LineFollowing_SetTargetAngle(0.0f);

        // 启动循迹功能
        LineFollowing_Start();

        control_started = true;

        OLED_Clear();
        OLED_ShowStr(0, 0, "LineFollow ON!", 16);
    }

    // 3. OLED显示更新 (低频率更新，避免影响控制性能)
    if (current_time - last_display_update >= 200) { // 200ms更新一次显示
        System_Status_Display();
        last_display_update = current_time;
    }

    // 4. 串口调试输出 (每秒输出一次)
    static uint32_t last_debug_output = 0;
    if (current_time - last_debug_output >= 1000) {
        System_Debug_Output();
        last_debug_output = current_time;
    }
}

/**
 * @brief 系统状态OLED显示
 */
void System_Status_Display(void)
{
    OLED_Clear();

    // 使用循迹系统内置的显示函数
    LineFollowing_DisplayStatus();
}

/**
 * @brief 系统调试信息串口输出
 */
void System_Debug_Output(void)
{
    // 获取循迹系统调试数据
    DebugData_t debug_data;
    LineFollowing_GetDebugData(&debug_data);

    // 获取控制数据
    float angle_error, line_error;
    int8_t left_speed, right_speed;
    LineFollowing_GetControlData(&angle_error, &line_error, &left_speed, &right_speed);

    // 输出调试信息
    printf("=== 循迹系统状态 (运行时间: %lu秒) ===\r\n",
           (HAL_GetTick() - system_start_time) / 1000);
    printf("系统状态: %s | 更新频率: %luHz | 更新计数: %lu\r\n",
           LineFollowing_GetStateString(), debug_data.update_frequency, control_update_count);
    printf("目标角度: %.1f° | 当前角度: %.1f° | 角度误差: %.2f°\r\n",
           debug_data.target_angle, debug_data.current_yaw, angle_error);
    printf("线路误差: %.2f | 检测传感器: %d个\r\n",
           line_error, debug_data.gray_sensor_count);
    printf("左电机: %d%% | 右电机: %d%% | 基础速度: 45%%\r\n",
           left_speed, right_speed);
    printf("传感器状态: 陀螺仪=%s | 灰度=%s\r\n",
           debug_data.angle_sensor_ok ? "OK" : "ERR",
           debug_data.gray_sensor_ok ? "OK" : "ERR");
    printf("控制输出: 角度=%.1f | 巡线=%.1f\r\n",
           debug_data.angle_control_out, debug_data.line_control_out);
    printf("========================================\r\n\r\n");
}

/**
 * @brief 循迹系统功能测试
 */
void LineFollowing_System_Test(void)
{
    printf("🧪 开始循迹系统功能测试...\r\n");

    // 测试1: 纯角度控制模式 (5秒)
    printf("测试1: 纯角度控制模式 (5秒)\r\n");
    OLED_Clear();
    OLED_ShowStr(0, 0, "Test: Angle", 16);
    OLED_ShowStr(0, 2, "Mode Only", 12);

    LineFollowing_SetMode(ANGLE_ONLY);
    LineFollowing_SetTargetAngle(0.0f);
    LineFollowing_SetBaseSpeed(30);
    LineFollowing_Start();

    uint32_t test_start = HAL_GetTick();
    while (HAL_GetTick() - test_start < 5000) {
        LineFollowing_Update();
        if ((HAL_GetTick() - test_start) % 200 == 0) {
            System_Status_Display();
        }
        HAL_Delay(10);
    }

    LineFollowing_Stop();
    HAL_Delay(1000);

    // 测试2: 纯巡线控制模式 (5秒)
    printf("测试2: 纯巡线控制模式 (5秒)\r\n");
    OLED_Clear();
    OLED_ShowStr(0, 0, "Test: Line", 16);
    OLED_ShowStr(0, 2, "Mode Only", 12);

    LineFollowing_SetMode(LINE_ONLY);
    LineFollowing_SetBaseSpeed(25);
    LineFollowing_Start();

    test_start = HAL_GetTick();
    while (HAL_GetTick() - test_start < 5000) {
        LineFollowing_Update();
        if ((HAL_GetTick() - test_start) % 200 == 0) {
            System_Status_Display();
        }
        HAL_Delay(10);
    }

    LineFollowing_Stop();
    HAL_Delay(1000);

    // 测试3: 融合控制模式 (10秒)
    printf("测试3: 融合控制模式 (10秒)\r\n");
    OLED_Clear();
    OLED_ShowStr(0, 0, "Test: Fusion", 16);
    OLED_ShowStr(0, 2, "Mode", 12);

    LineFollowing_SetMode(FUSION);
    LineFollowing_SetTargetAngle(0.0f);
    LineFollowing_SetBaseSpeed(40);
    LineFollowing_Start();

    test_start = HAL_GetTick();
    while (HAL_GetTick() - test_start < 10000) {
        LineFollowing_Update();
        if ((HAL_GetTick() - test_start) % 200 == 0) {
            System_Status_Display();
        }
        HAL_Delay(10);
    }

    LineFollowing_Stop();

    printf("✅ 循迹系统功能测试完成!\r\n");
    OLED_Clear();
    OLED_ShowStr(0, 0, "Test Complete!", 16);
    OLED_ShowStr(0, 2, "All Functions", 12);
    OLED_ShowStr(0, 4, "Working OK!", 12);
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_I2C2_Init();
  MX_TIM1_Init();
  MX_UART4_Init();
  MX_UART5_Init();
  MX_USART2_UART_Init();
  MX_USART6_UART_Init();

  /* USER CODE BEGIN 2 */

  /* 循迹系统完整初始化 */
  LineFollowing_System_Init();

  /* 可选：运行系统功能测试 (取消注释以启用测试) */
  // LineFollowing_System_Test();

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* 循迹系统主循环 (20Hz控制频率) */
    LineFollowing_Main_Loop();

    /* 控制循环延时 (50ms = 20Hz) */
    HAL_Delay(50);

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 180;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Activate the Over-Drive mode
  */
  if (HAL_PWREx_EnableOverDrive() != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief UART接收完成中断回调函数
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == UART5)
    {
        // JY901S数据处理
        JY901S_ProcessByte(&jy901s_handle, uart_rx_buffer[0]);

        // 重新启动接收中断
        HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
    }
}

/**
 * @brief GPIO初始化
 */
static void MX_GPIO_Init(void)
{
  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();
  __HAL_RCC_GPIOE_CLK_ENABLE();
}

/**
 * @brief DMA初始化
 */
static void MX_DMA_Init(void)
{
  /* DMA controller clock enable */
  __HAL_RCC_DMA1_CLK_ENABLE();
  __HAL_RCC_DMA2_CLK_ENABLE();
}

/**
 * @brief I2C1初始化
 */
static void MX_I2C1_Init(void)
{
  /* I2C1 clock enable */
  __HAL_RCC_I2C1_CLK_ENABLE();
}

/**
 * @brief I2C2初始化
 */
static void MX_I2C2_Init(void)
{
  /* I2C2 clock enable */
  __HAL_RCC_I2C2_CLK_ENABLE();

  hi2c2.Instance = I2C2;
  hi2c2.Init.ClockSpeed = 100000;
  hi2c2.Init.DutyCycle = I2C_DUTYCYCLE_2;
  hi2c2.Init.OwnAddress1 = 0;
  hi2c2.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
  hi2c2.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
  hi2c2.Init.OwnAddress2 = 0;
  hi2c2.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
  hi2c2.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
  if (HAL_I2C_Init(&hi2c2) != HAL_OK)
  {
    Error_Handler();
  }
}

/**
 * @brief TIM1初始化 (电机PWM控制)
 */
static void MX_TIM1_Init(void)
{
  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
  TIM_MasterConfigTypeDef sMasterConfig = {0};
  TIM_OC_InitTypeDef sConfigOC = {0};
  TIM_BreakDeadTimeConfigTypeDef sBreakDeadTimeConfig = {0};

  /* TIM1 clock enable */
  __HAL_RCC_TIM1_CLK_ENABLE();

  htim1.Instance = TIM1;
  htim1.Init.Prescaler = 180-1;        // 180MHz/180 = 1MHz
  htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim1.Init.Period = 1000-1;          // 1MHz/1000 = 1kHz PWM频率
  htim1.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim1.Init.RepetitionCounter = 0;
  htim1.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
  if (HAL_TIM_Base_Init(&htim1) != HAL_OK)
  {
    Error_Handler();
  }
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
  if (HAL_TIM_ConfigClockSource(&htim1, &sClockSourceConfig) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_PWM_Init(&htim1) != HAL_OK)
  {
    Error_Handler();
  }
  sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
  sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
  if (HAL_TIMEx_MasterConfigSynchronization(&htim1, &sMasterConfig) != HAL_OK)
  {
    Error_Handler();
  }
  sConfigOC.OCMode = TIM_OCMODE_PWM1;
  sConfigOC.Pulse = 0;
  sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
  sConfigOC.OCNPolarity = TIM_OCNPOLARITY_HIGH;
  sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
  sConfigOC.OCIdleState = TIM_OCIDLESTATE_RESET;
  sConfigOC.OCNIdleState = TIM_OCNIDLESTATE_RESET;
  if (HAL_TIM_PWM_ConfigChannel(&htim1, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_PWM_ConfigChannel(&htim1, &sConfigOC, TIM_CHANNEL_2) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_PWM_ConfigChannel(&htim1, &sConfigOC, TIM_CHANNEL_3) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_PWM_ConfigChannel(&htim1, &sConfigOC, TIM_CHANNEL_4) != HAL_OK)
  {
    Error_Handler();
  }
  sBreakDeadTimeConfig.OffStateRunMode = TIM_OSSR_DISABLE;
  sBreakDeadTimeConfig.OffStateIDLEMode = TIM_OSSI_DISABLE;
  sBreakDeadTimeConfig.LockLevel = TIM_LOCKLEVEL_OFF;
  sBreakDeadTimeConfig.DeadTime = 0;
  sBreakDeadTimeConfig.BreakState = TIM_BREAK_DISABLE;
  sBreakDeadTimeConfig.BreakPolarity = TIM_BREAKPOLARITY_HIGH;
  sBreakDeadTimeConfig.AutomaticOutput = TIM_AUTOMATICOUTPUT_DISABLE;
  if (HAL_TIMEx_ConfigBreakDeadTime(&htim1, &sBreakDeadTimeConfig) != HAL_OK)
  {
    Error_Handler();
  }

  /* 启动PWM输出 */
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2);
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_3);
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_4);
}

/**
 * @brief UART4初始化 (备用串口)
 */
static void MX_UART4_Init(void)
{
  huart4.Instance = UART4;
  huart4.Init.BaudRate = 9600;
  huart4.Init.WordLength = UART_WORDLENGTH_8B;
  huart4.Init.StopBits = UART_STOPBITS_1;
  huart4.Init.Parity = UART_PARITY_NONE;
  huart4.Init.Mode = UART_MODE_TX_RX;
  huart4.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart4.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart4) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief UART5初始化 (JY901S姿态传感器)
 */
static void MX_UART5_Init(void)
{
  huart5.Instance = UART5;
  huart5.Init.BaudRate = 9600;
  huart5.Init.WordLength = UART_WORDLENGTH_8B;
  huart5.Init.StopBits = UART_STOPBITS_1;
  huart5.Init.Parity = UART_PARITY_NONE;
  huart5.Init.Mode = UART_MODE_TX_RX;
  huart5.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart5.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart5) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief USART2初始化 (备用串口)
 */
static void MX_USART2_UART_Init(void)
{
  huart2.Instance = USART2;
  huart2.Init.BaudRate = 9600;
  huart2.Init.WordLength = UART_WORDLENGTH_8B;
  huart2.Init.StopBits = UART_STOPBITS_1;
  huart2.Init.Parity = UART_PARITY_NONE;
  huart2.Init.Mode = UART_MODE_TX_RX;
  huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart2.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart2) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief USART6初始化 (K230D数据接收)
 */
static void MX_USART6_UART_Init(void)
{
  huart6.Instance = USART6;
  huart6.Init.BaudRate = 115200;
  huart6.Init.WordLength = UART_WORDLENGTH_8B;
  huart6.Init.StopBits = UART_STOPBITS_1;
  huart6.Init.Parity = UART_PARITY_NONE;
  huart6.Init.Mode = UART_MODE_TX_RX;
  huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart6.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart6) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief UART MSP初始化
 */
void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  if(uartHandle->Instance==UART4)
  {
    /* UART4 clock enable */
    __HAL_RCC_UART4_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* UART4 GPIO Configuration: PC10->TX, PC11->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_10|GPIO_PIN_11;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART4;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==UART5)
  {
    /* UART5 clock enable */
    __HAL_RCC_UART5_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();

    /* UART5 GPIO Configuration: PC12->TX, PD2->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_12;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART5;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_2;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART5;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==USART2)
  {
    /* USART2 clock enable */
    __HAL_RCC_USART2_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /* USART2 GPIO Configuration: PA2->TX, PA3->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART2;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==USART6)
  {
    /* USART6 clock enable */
    __HAL_RCC_USART6_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* USART6 GPIO Configuration: PC6->TX, PC7->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_USART6;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
  }
}

/**
 * @brief 系统错误处理函数
 */
void System_ErrorHandler(void)
{
  printf("❌ 系统错误！进入错误处理模式\r\n");

  // OLED显示错误信息
  OLED_Clear();
  OLED_ShowStr(0, 0, "SYSTEM ERROR!", 16);
  OLED_ShowStr(0, 2, "Check Hardware", 12);
  OLED_ShowStr(0, 4, "Reset Required", 12);

  // 停止所有电机
  if (system_initialized) {
    LineFollowing_Stop();
  }

  // 错误指示灯闪烁
  while(1) {
    HAL_Delay(500);
  }
}

/**
 * @brief 错误处理函数
 */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  System_ErrorHandler();
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  printf("Assert failed: file %s on line %lu\r\n", file, line);
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/* USER CODE END 4 */





/**
  * @brief I2C1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_I2C1_Init(void)
{
  /* USER CODE BEGIN I2C1_Init 0 */

  /* USER CODE END I2C1_Init 0 */

  /* USER CODE BEGIN I2C1_Init 1 */

  /* USER CODE END I2C1_Init 1 */
  hi2c1.Instance = I2C1;
  hi2c1.Init.ClockSpeed = 100000;
  hi2c1.Init.DutyCycle = I2C_DUTYCYCLE_2;
  hi2c1.Init.OwnAddress1 = 0;
  hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
  hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
  hi2c1.Init.OwnAddress2 = 0;
  hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
  hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
  if (HAL_I2C_Init(&hi2c1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN I2C1_Init 2 */

  /* USER CODE END I2C1_Init 2 */
}

/**
  * @brief USART6 Initialization Function
  * @param None
  * @retval None
  */
static void MX_USART6_UART_Init(void)
{
  /* USER CODE BEGIN USART6_Init 0 */

  /* USER CODE END USART6_Init 0 */

  /* USER CODE BEGIN USART6_Init 1 */

  /* USER CODE END USART6_Init 1 */
  huart6.Instance = USART6;
  huart6.Init.BaudRate = 115200;
  huart6.Init.WordLength = UART_WORDLENGTH_8B;
  huart6.Init.StopBits = UART_STOPBITS_1;
  huart6.Init.Parity = UART_PARITY_NONE;
  huart6.Init.Mode = UART_MODE_TX_RX;
  huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart6.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart6) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART6_Init 2 */

  /* USER CODE END USART6_Init 2 */
}

/**
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOC_CLK_ENABLE();
}

/**
  * @brief DMA Initialization Function
  * @param None
  * @retval None
  */
static void MX_DMA_Init(void)
{
  /* DMA controller clock enable */
  __HAL_RCC_DMA2_CLK_ENABLE();
}

/* USER CODE END 4 */
