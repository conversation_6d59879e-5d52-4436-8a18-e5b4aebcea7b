#include "line_following.h"
#include "oled.h"
#include <stdio.h>
#include <string.h>

/**********************************************************
***	循迹功能模块实现
***	编写作者：Alex (工程师)
***	功能：基于JY901S+灰度传感器的智能循迹
***	版本：V1.0 - 完整功能版
***	日期：2025-07-29
**********************************************************/

// 外部变量声明
extern JY901S_Handle jy901s_handle;
extern JY901S_Data sensor_data;
extern uint8_t gray_digital_data;
extern uint8_t gray_analog_data[8];
extern Motor_Handle motor_left;
extern Motor_Handle motor_right;

// 全局变量
static AngleControl_t g_angle_ctrl;
static LineControl_t g_line_ctrl;
static MotorControl_t g_motor_ctrl;
static LineFollowing_Config_t g_config;
static ControlData_t g_control_data;
static bool g_initialized = false;
static bool g_running = false;

// 状态字符串
static const char* state_strings[] = {"IDLE", "RUN", "ERR", "CAL"};

// 默认配置
static const LineFollowing_Config_t default_config = {
    // 角度PID参数
    .angle_kp = 2.0f,
    .angle_ki = 0.1f, 
    .angle_kd = 0.5f,
    .angle_output_limit = 50.0f,
    
    // 巡线PID参数
    .line_kp = 1.5f,
    .line_ki = 0.05f,
    .line_kd = 0.3f,
    .line_output_limit = 40.0f,
    
    // 传感器权重 (从左到右，外侧权重更大)
    .line_weights = {-4.0f, -2.0f, -1.0f, -0.5f, 0.5f, 1.0f, 2.0f, 4.0f},
    
    // 电机参数
    .base_speed = 50,       // 50%基础速度
    .max_correction = 30,   // 最大30%修正
    
    // 系统参数
    .mode = FUSION,
    .update_period = 50,    // 50ms更新周期 (20Hz)
    .debug_enabled = true
};

/**********************************************************
*** 核心API接口实现
**********************************************************/

/**
  * @brief 初始化循迹系统
  */
HAL_StatusTypeDef LineFollowing_Init(LineFollowing_Config_t* config)
{
    // 使用默认配置或用户配置
    if (config != NULL) {
        g_config = *config;
    } else {
        g_config = default_config;
    }
    
    // 初始化角度控制器
    g_angle_ctrl.kp = g_config.angle_kp;
    g_angle_ctrl.ki = g_config.angle_ki;
    g_angle_ctrl.kd = g_config.angle_kd;
    g_angle_ctrl.output_limit = g_config.angle_output_limit;
    g_angle_ctrl.integral = 0;
    g_angle_ctrl.last_error = 0;
    g_angle_ctrl.target_angle = 0;
    g_angle_ctrl.enabled = (g_config.mode != LINE_ONLY);
    g_angle_ctrl.update_period = g_config.update_period;
    g_angle_ctrl.last_update = HAL_GetTick();
    
    // 初始化巡线控制器
    g_line_ctrl.kp = g_config.line_kp;
    g_line_ctrl.ki = g_config.line_ki;
    g_line_ctrl.kd = g_config.line_kd;
    g_line_ctrl.output_limit = g_config.line_output_limit;
    g_line_ctrl.integral = 0;
    g_line_ctrl.last_error = 0;
    g_line_ctrl.sensor_threshold = 128; // 默认阈值
    g_line_ctrl.enabled = (g_config.mode != ANGLE_ONLY);
    g_line_ctrl.last_update = HAL_GetTick();
    
    // 复制传感器权重
    memcpy(g_line_ctrl.weights, g_config.line_weights, sizeof(g_config.line_weights));
    
    // 初始化电机控制器
    g_motor_ctrl.left_motor = &motor_left;
    g_motor_ctrl.right_motor = &motor_right;
    g_motor_ctrl.base_speed = g_config.base_speed;
    g_motor_ctrl.max_correction = g_config.max_correction;
    g_motor_ctrl.angle_control = 0;
    g_motor_ctrl.line_control = 0;
    g_motor_ctrl.left_speed = 0;
    g_motor_ctrl.right_speed = 0;
    g_motor_ctrl.enabled = false;
    
    // 初始化控制数据
    g_control_data.target_angle = 0;
    g_control_data.mode = g_config.mode;
    g_control_data.base_speed = g_config.base_speed;
    g_control_data.state = LF_IDLE;
    g_control_data.update_count = 0;
    g_control_data.last_update = HAL_GetTick();
    
    g_initialized = true;
    
    return HAL_OK;
}

/**
  * @brief 循迹系统主循环更新
  */
void LineFollowing_Update(void)
{
    if (!g_initialized || !g_running) {
        return;
    }
    
    uint32_t current_time = HAL_GetTick();
    
    // 检查更新周期
    if (current_time - g_control_data.last_update < g_config.update_period) {
        return;
    }
    
    g_control_data.update_count++;
    g_control_data.last_update = current_time;
    
    // 1. 数据采集
    SensorData_t sensor_data_local;
    if (SensorFusion_GetData(&sensor_data_local) != HAL_OK) {
        g_control_data.state = LF_ERROR;
        return;
    }
    
    // 2. 角度控制
    float angle_output = 0;
    if (g_config.mode != LINE_ONLY && g_angle_ctrl.enabled) {
        angle_output = AngleControl_Update(&g_angle_ctrl, 
                                          g_control_data.target_angle, 
                                          sensor_data_local.yaw_angle);
        g_control_data.angle_output = angle_output;
        g_control_data.angle_error = g_angle_ctrl.angle_error;
    }
    
    // 3. 巡线控制  
    float line_output = 0;
    if (g_config.mode != ANGLE_ONLY && g_line_ctrl.enabled) {
        line_output = LineControl_Update(&g_line_ctrl, 
                                        sensor_data_local.gray_digital,
                                        sensor_data_local.gray_analog);
        g_control_data.line_output = line_output;
        g_control_data.line_error = g_line_ctrl.line_error;
    }
    
    // 4. 电机控制
    MotorControl_SetInputs(&g_motor_ctrl, angle_output, line_output);
    MotorControl_Update(&g_motor_ctrl);
    
    // 5. 更新控制数据
    g_control_data.left_motor_speed = g_motor_ctrl.left_speed;
    g_control_data.right_motor_speed = g_motor_ctrl.right_speed;
    g_control_data.state = LF_RUNNING;
}

/**
  * @brief 启动循迹功能
  */
void LineFollowing_Start(void)
{
    if (!g_initialized) {
        return;
    }
    
    // 重置控制器状态
    LineFollowing_Reset();
    
    // 使能电机控制
    g_motor_ctrl.enabled = true;
    
    g_running = true;
    g_control_data.state = LF_RUNNING;
}

/**
  * @brief 停止循迹功能
  */
void LineFollowing_Stop(void)
{
    g_running = false;
    g_motor_ctrl.enabled = false;
    
    // 停止电机
    Motor_SetSpeed(g_motor_ctrl.left_motor, 0);
    Motor_SetSpeed(g_motor_ctrl.right_motor, 0);
    
    g_control_data.state = LF_IDLE;
}

/**
  * @brief 设定目标角度
  */
void LineFollowing_SetTargetAngle(float target_angle)
{
    // 角度范围限制
    while (target_angle > 180.0f) target_angle -= 360.0f;
    while (target_angle < -180.0f) target_angle += 360.0f;
    
    g_control_data.target_angle = target_angle;
    g_angle_ctrl.target_angle = target_angle;
}

/**
  * @brief 设定工作模式
  */
void LineFollowing_SetMode(LineFollowing_Mode_t mode)
{
    // 模式切换时重置控制器状态
    AngleControl_Reset(&g_angle_ctrl);
    LineControl_Reset(&g_line_ctrl);
    
    g_config.mode = mode;
    g_control_data.mode = mode;
    
    // 根据模式配置控制器使能
    g_angle_ctrl.enabled = (mode != LINE_ONLY);
    g_line_ctrl.enabled = (mode != ANGLE_ONLY);
}

/**
  * @brief 设定基础速度
  */
void LineFollowing_SetBaseSpeed(uint8_t speed)
{
    if (speed > 100) speed = 100;
    
    g_config.base_speed = speed;
    g_control_data.base_speed = speed;
    g_motor_ctrl.base_speed = speed;
}

/**********************************************************
*** 状态查询接口实现
**********************************************************/

/**
  * @brief 获取当前系统状态
  */
LineFollowing_State_t LineFollowing_GetState(void)
{
    return g_control_data.state;
}

/**
  * @brief 获取控制数据
  */
void LineFollowing_GetControlData(float* angle_error, float* line_error, 
                                  int8_t* left_speed, int8_t* right_speed)
{
    if (angle_error) *angle_error = g_control_data.angle_error;
    if (line_error) *line_error = g_control_data.line_error;
    if (left_speed) *left_speed = g_control_data.left_motor_speed;
    if (right_speed) *right_speed = g_control_data.right_motor_speed;
}

/**
  * @brief 获取调试数据
  */
void LineFollowing_GetDebugData(DebugData_t* debug_data)
{
    if (!debug_data) return;
    
    debug_data->current_yaw = g_angle_ctrl.current_angle;
    debug_data->target_angle = g_control_data.target_angle;
    debug_data->angle_error = g_control_data.angle_error;
    debug_data->line_error = g_control_data.line_error;
    debug_data->angle_control_out = g_control_data.angle_output;
    debug_data->line_control_out = g_control_data.line_output;
    debug_data->left_motor_speed = g_control_data.left_motor_speed;
    debug_data->right_motor_speed = g_control_data.right_motor_speed;
    debug_data->system_state = g_control_data.state;
    debug_data->error_count = 0; // TODO: 实现错误计数
    
    // 计算更新频率
    static uint32_t last_count = 0;
    static uint32_t last_time = 0;
    uint32_t current_time = HAL_GetTick();
    if (current_time - last_time >= 1000) {
        debug_data->update_frequency = g_control_data.update_count - last_count;
        last_count = g_control_data.update_count;
        last_time = current_time;
    }
    
    // 传感器状态检查
    debug_data->angle_sensor_ok = (SensorFusion_CheckHealth() == SENSOR_OK);
    debug_data->gray_sensor_ok = true; // TODO: 实现灰度传感器健康检查
    
    // 计算检测到线的传感器数量
    debug_data->gray_sensor_count = 0;
    for (int i = 0; i < 8; i++) {
        if (g_line_ctrl.digital_data & (1 << i)) {
            debug_data->gray_sensor_count++;
        }
    }
}

/**
  * @brief 获取状态字符串
  */
const char* LineFollowing_GetStateString(void)
{
    if (g_control_data.state < 4) {
        return state_strings[g_control_data.state];
    }
    return "UNK";
}

/**********************************************************
*** 配置接口实现
**********************************************************/

/**
  * @brief 设置角度PID参数
  */
void LineFollowing_SetAnglePID(float kp, float ki, float kd)
{
    g_angle_ctrl.kp = kp;
    g_angle_ctrl.ki = ki;
    g_angle_ctrl.kd = kd;
    g_config.angle_kp = kp;
    g_config.angle_ki = ki;
    g_config.angle_kd = kd;
}

/**
  * @brief 设置巡线PID参数
  */
void LineFollowing_SetLinePID(float kp, float ki, float kd)
{
    g_line_ctrl.kp = kp;
    g_line_ctrl.ki = ki;
    g_line_ctrl.kd = kd;
    g_config.line_kp = kp;
    g_config.line_ki = ki;
    g_config.line_kd = kd;
}

/**
  * @brief 设置传感器权重
  */
void LineFollowing_SetSensorWeights(float weights[8])
{
    memcpy(g_line_ctrl.weights, weights, sizeof(g_line_ctrl.weights));
    memcpy(g_config.line_weights, weights, sizeof(g_config.line_weights));
}

/**
  * @brief 重置控制器状态
  */
void LineFollowing_Reset(void)
{
    AngleControl_Reset(&g_angle_ctrl);
    LineControl_Reset(&g_line_ctrl);
    
    g_control_data.update_count = 0;
    g_control_data.last_update = HAL_GetTick();
}

/**********************************************************
*** OLED显示集成接口实现
**********************************************************/

/**
  * @brief OLED显示循迹状态
  */
void LineFollowing_DisplayStatus(void)
{
    DebugData_t debug_data;
    LineFollowing_GetDebugData(&debug_data);

    // 第1行：目标角度和当前角度
    OLED_ShowStr(0, 0, "T:", 8);
    OLED_ShowSignedAngle(16, 0, (int16_t)debug_data.target_angle);
    OLED_ShowStr(48, 0, "C:", 8);
    OLED_ShowSignedAngle(64, 0, (int16_t)debug_data.current_yaw);

    // 第2行：角度误差和线路误差
    OLED_ShowStr(0, 1, "AE:", 8);
    OLED_ShowSignedAngle(24, 1, (int16_t)debug_data.angle_error);
    OLED_ShowStr(56, 1, "LE:", 8);
    OLED_ShowSignedAngle(80, 1, (int16_t)(debug_data.line_error * 10));

    // 第3行：电机速度
    OLED_ShowStr(0, 2, "L:", 8);
    OLED_ShowSignedNum(16, 2, debug_data.left_motor_speed, 3, 8);
    OLED_ShowStr(48, 2, "R:", 8);
    OLED_ShowSignedNum(64, 2, debug_data.right_motor_speed, 3, 8);

    // 第4行：系统状态和传感器状态
    OLED_ShowStr(0, 3, (uint8_t*)LineFollowing_GetStateString(), 8);
    OLED_ShowStr(32, 3, "S:", 8);
    OLED_ShowNum(40, 3, debug_data.gray_sensor_count, 1, 8);
    OLED_ShowStr(48, 3, "F:", 8);
    OLED_ShowNum(56, 3, debug_data.update_frequency, 2, 8);
}

/**********************************************************
*** 内部函数实现
**********************************************************/

/**
  * @brief 传感器数据融合
  */
HAL_StatusTypeDef SensorFusion_GetData(SensorData_t* sensor_data_local)
{
    if (!sensor_data_local) return HAL_ERROR;

    uint32_t current_time = HAL_GetTick();

    // 获取JY901S角度数据
    if (JY901S_GetData(&jy901s_handle, &sensor_data) == HAL_OK) {
        sensor_data_local->yaw_angle = sensor_data.angle[2];    // Yaw角度
        sensor_data_local->roll_angle = sensor_data.angle[0];   // Roll角度
        sensor_data_local->pitch_angle = sensor_data.angle[1];  // Pitch角度
        sensor_data_local->angle_timestamp = current_time;
        sensor_data_local->angle_valid = true;
    } else {
        sensor_data_local->angle_valid = false;
    }

    // 获取灰度传感器数据
    if (GrayScale_ReadData() == HAL_OK) {
        sensor_data_local->gray_digital = gray_digital_data;
        memcpy(sensor_data_local->gray_analog, gray_analog_data, 8);
        sensor_data_local->gray_timestamp = current_time;
        sensor_data_local->gray_valid = true;
    } else {
        sensor_data_local->gray_valid = false;
    }

    // 至少需要一个传感器数据有效
    if (!sensor_data_local->angle_valid && !sensor_data_local->gray_valid) {
        return HAL_ERROR;
    }

    return HAL_OK;
}

/**
  * @brief 传感器健康检查
  */
SensorError_t SensorFusion_CheckHealth(void)
{
    uint32_t current_time = HAL_GetTick();

    // 检查JY901S数据更新时间
    static uint32_t last_angle_time = 0;
    if (current_time - last_angle_time > 200) { // 200ms超时
        return SENSOR_TIMEOUT;
    }

    // 检查角度数据合理性
    if (g_angle_ctrl.current_angle < -180 || g_angle_ctrl.current_angle > 180) {
        return SENSOR_DATA_INVALID;
    }

    return SENSOR_OK;
}

/**
  * @brief 角度控制更新
  */
float AngleControl_Update(AngleControl_t* ctrl, float target, float current)
{
    if (!ctrl || !ctrl->enabled) return 0;

    uint32_t current_time = HAL_GetTick();
    if (current_time - ctrl->last_update < ctrl->update_period) {
        return ctrl->control_output;
    }

    ctrl->last_update = current_time;
    ctrl->target_angle = target;
    ctrl->current_angle = current;

    // 计算角度误差 (处理角度环绕)
    float error = target - current;
    if (error > 180.0f) error -= 360.0f;
    if (error < -180.0f) error += 360.0f;

    ctrl->angle_error = error;

    // PID计算
    ctrl->integral += error;

    // 积分限幅
    float integral_limit = ctrl->output_limit / ctrl->ki;
    if (ctrl->integral > integral_limit) ctrl->integral = integral_limit;
    if (ctrl->integral < -integral_limit) ctrl->integral = -integral_limit;

    float derivative = error - ctrl->last_error;

    float output = ctrl->kp * error +
                   ctrl->ki * ctrl->integral +
                   ctrl->kd * derivative;

    // 输出限制
    if (output > ctrl->output_limit) output = ctrl->output_limit;
    if (output < -ctrl->output_limit) output = -ctrl->output_limit;

    ctrl->last_error = error;
    ctrl->control_output = output;

    return output;
}

/**
  * @brief 巡线控制更新
  */
float LineControl_Update(LineControl_t* ctrl, uint8_t digital, uint8_t* analog)
{
    if (!ctrl || !ctrl->enabled) return 0;

    ctrl->digital_data = digital;
    if (analog) {
        memcpy(ctrl->analog_data, analog, 8);
    }

    // 计算线路误差 (权重化算法)
    float weighted_sum = 0;
    float total_active = 0;

    for (int i = 0; i < 8; i++) {
        if (digital & (1 << i)) {
            weighted_sum += ctrl->weights[i];
            total_active += 1.0f;
        }
    }

    // 计算归一化误差
    float line_error = 0;
    if (total_active > 0) {
        line_error = weighted_sum / total_active;
    } else {
        // 无线检测，保持上次误差
        line_error = ctrl->last_error;
    }

    ctrl->line_error = line_error;

    // PID计算
    ctrl->integral += line_error;

    // 积分限幅
    float integral_limit = ctrl->output_limit / ctrl->ki;
    if (ctrl->integral > integral_limit) ctrl->integral = integral_limit;
    if (ctrl->integral < -integral_limit) ctrl->integral = -integral_limit;

    float derivative = line_error - ctrl->last_error;

    float output = ctrl->kp * line_error +
                   ctrl->ki * ctrl->integral +
                   ctrl->kd * derivative;

    // 输出限制
    if (output > ctrl->output_limit) output = ctrl->output_limit;
    if (output < -ctrl->output_limit) output = -ctrl->output_limit;

    ctrl->last_error = line_error;
    ctrl->control_output = output;

    return output;
}

/**
  * @brief 电机控制输入设置
  */
void MotorControl_SetInputs(MotorControl_t* ctrl, float angle_input, float line_input)
{
    if (!ctrl) return;

    ctrl->angle_control = angle_input;
    ctrl->line_control = line_input;
}

/**
  * @brief 电机控制更新
  */
void MotorControl_Update(MotorControl_t* ctrl)
{
    if (!ctrl || !ctrl->enabled) {
        // 停止电机
        if (ctrl->left_motor) Motor_SetSpeed(ctrl->left_motor, 0);
        if (ctrl->right_motor) Motor_SetSpeed(ctrl->right_motor, 0);
        ctrl->left_speed = 0;
        ctrl->right_speed = 0;
        return;
    }

    // 控制信号融合
    float total_correction = ctrl->angle_control + ctrl->line_control;

    // 限制修正幅度
    if (total_correction > ctrl->max_correction)
        total_correction = ctrl->max_correction;
    if (total_correction < -ctrl->max_correction)
        total_correction = -ctrl->max_correction;

    // 差速计算
    float left_speed = ctrl->base_speed + total_correction;
    float right_speed = ctrl->base_speed - total_correction;

    // 速度限制
    left_speed = CLAMP(left_speed, -100, 100);
    right_speed = CLAMP(right_speed, -100, 100);

    // 输出到电机
    if (ctrl->left_motor) {
        Motor_SetSpeed(ctrl->left_motor, left_speed / 100.0f);  // 转换为-1.0~1.0范围
    }
    if (ctrl->right_motor) {
        Motor_SetSpeed(ctrl->right_motor, right_speed / 100.0f);
    }

    // 保存状态
    ctrl->left_speed = (int8_t)left_speed;
    ctrl->right_speed = (int8_t)right_speed;
}

/**
  * @brief 角度控制器重置
  */
void AngleControl_Reset(AngleControl_t* ctrl)
{
    if (!ctrl) return;

    ctrl->integral = 0;
    ctrl->last_error = 0;
    ctrl->angle_error = 0;
    ctrl->control_output = 0;
    ctrl->last_update = HAL_GetTick();
}

/**
  * @brief 巡线控制器重置
  */
void LineControl_Reset(LineControl_t* ctrl)
{
    if (!ctrl) return;

    ctrl->integral = 0;
    ctrl->last_error = 0;
    ctrl->line_error = 0;
    ctrl->control_output = 0;
    ctrl->last_update = HAL_GetTick();

    // 清空传感器数据
    ctrl->digital_data = 0;
    memset(ctrl->analog_data, 0, sizeof(ctrl->analog_data));
}

/**
  * @brief 电机控制输入设置
  */
void MotorControl_SetInputs(MotorControl_t* ctrl, float angle_input, float line_input)
{
    if (!ctrl) return;

    ctrl->angle_control = angle_input;
    ctrl->line_control = line_input;
}

/**
  * @brief 电机控制更新
  */
void MotorControl_Update(MotorControl_t* ctrl)
{
    if (!ctrl || !ctrl->enabled) {
        // 停止电机
        if (ctrl->left_motor) Motor_SetSpeed(ctrl->left_motor, 0);
        if (ctrl->right_motor) Motor_SetSpeed(ctrl->right_motor, 0);
        ctrl->left_speed = 0;
        ctrl->right_speed = 0;
        return;
    }

    // 控制信号融合
    float total_correction = ctrl->angle_control + ctrl->line_control;

    // 限制修正幅度
    if (total_correction > ctrl->max_correction)
        total_correction = ctrl->max_correction;
    if (total_correction < -ctrl->max_correction)
        total_correction = -ctrl->max_correction;

    // 差速计算
    float left_speed = ctrl->base_speed + total_correction;
    float right_speed = ctrl->base_speed - total_correction;

    // 速度限制
    left_speed = CLAMP(left_speed, -100, 100);
    right_speed = CLAMP(right_speed, -100, 100);

    // 输出到电机
    if (ctrl->left_motor) {
        Motor_SetSpeed(ctrl->left_motor, left_speed / 100.0f);  // 转换为-1.0~1.0范围
    }
    if (ctrl->right_motor) {
        Motor_SetSpeed(ctrl->right_motor, right_speed / 100.0f);
    }

    // 保存状态
    ctrl->left_speed = (int8_t)left_speed;
    ctrl->right_speed = (int8_t)right_speed;
}

/**
  * @brief 角度控制器重置
  */
void AngleControl_Reset(AngleControl_t* ctrl)
{
    if (!ctrl) return;

    ctrl->integral = 0;
    ctrl->last_error = 0;
    ctrl->angle_error = 0;
    ctrl->control_output = 0;
    ctrl->last_update = HAL_GetTick();
}

/**
  * @brief 巡线控制器重置
  */
void LineControl_Reset(LineControl_t* ctrl)
{
    if (!ctrl) return;

    ctrl->integral = 0;
    ctrl->last_error = 0;
    ctrl->line_error = 0;
    ctrl->control_output = 0;
    ctrl->last_update = HAL_GetTick();

    // 清空传感器数据
    ctrl->digital_data = 0;
    memset(ctrl->analog_data, 0, sizeof(ctrl->analog_data));
}
