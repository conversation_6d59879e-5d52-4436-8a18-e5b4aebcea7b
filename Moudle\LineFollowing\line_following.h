#ifndef __LINE_FOLLOWING_H
#define __LINE_FOLLOWING_H

#include "main.h"
#include "stdbool.h"
#include "jy901s.h"
#include "motor_driver.h"
#include "software_iic.h"

/**********************************************************
***	循迹功能模块头文件
***	编写作者：Alex (工程师)
***	功能：基于JY901S+灰度传感器的智能循迹
***	版本：V1.0 - 完整功能版
***	日期：2025-07-29
**********************************************************/

// 宏定义
#ifndef ABS
#define ABS(x) ((x) > 0 ? (x) : -(x))
#endif

#ifndef CLAMP
#define CLAMP(x, min, max) ((x) < (min) ? (min) : ((x) > (max) ? (max) : (x)))
#endif

// 控制模式枚举
typedef enum {
    ANGLE_ONLY = 0,     // 纯角度控制模式
    LINE_ONLY,          // 纯巡线控制模式  
    FUSION              // 融合控制模式 (推荐)
} LineFollowing_Mode_t;

// 系统状态枚举
typedef enum {
    LF_IDLE = 0,        // 空闲状态
    LF_RUNNING,         // 运行状态
    LF_ERROR,           // 错误状态
    LF_CALIBRATING      // 校准状态
} LineFollowing_State_t;

// 传感器错误类型
typedef enum {
    SENSOR_OK = 0,
    SENSOR_TIMEOUT,
    SENSOR_DATA_INVALID,
    SENSOR_HARDWARE_ERROR
} SensorError_t;

// 角度控制器结构体
typedef struct {
    // PID控制器参数
    float kp, ki, kd;           // PID系数
    float integral;             // 积分累积
    float last_error;           // 上次误差
    float output_limit;         // 输出限制
    
    // 控制状态
    float target_angle;         // 目标角度
    float current_angle;        // 当前角度
    float angle_error;          // 角度误差
    float control_output;       // 控制输出
    
    // 配置参数
    uint32_t update_period;     // 更新周期(ms)
    uint32_t last_update;       // 上次更新时间
    bool enabled;               // 使能标志
} AngleControl_t;

// 巡线控制器结构体
typedef struct {
    // 传感器权重配置
    float weights[8];           // 8路传感器权重
    uint8_t sensor_threshold;   // 数字化阈值
    
    // PID控制参数
    float kp, ki, kd;           // PID系数
    float integral;             // 积分累积
    float last_error;           // 上次误差
    float output_limit;         // 输出限制
    
    // 状态数据
    uint8_t digital_data;       // 数字量数据
    uint8_t analog_data[8];     // 模拟量数据
    float line_error;           // 线路误差
    float control_output;       // 控制输出
    
    // 配置参数
    bool enabled;               // 使能标志
    uint32_t last_update;       // 上次更新时间
} LineControl_t;

// 电机控制器结构体
typedef struct {
    // 电机句柄
    Motor_t* left_motor;        // 左电机句柄
    Motor_t* right_motor;       // 右电机句柄
    
    // 控制参数
    uint8_t base_speed;         // 基础速度 (0-100)
    float max_correction;       // 最大修正幅度
    
    // 控制输入
    float angle_control;        // 角度控制输入
    float line_control;         // 巡线控制输入
    
    // 输出状态
    int8_t left_speed;          // 左电机速度
    int8_t right_speed;         // 右电机速度
    
    // 配置参数
    bool enabled;               // 使能标志
} MotorControl_t;

// 传感器数据结构
typedef struct {
    // JY901S数据
    float yaw_angle;            // Yaw角度
    float roll_angle;           // Roll角度  
    float pitch_angle;          // Pitch角度
    uint32_t angle_timestamp;   // 角度数据时间戳
    
    // 灰度传感器数据
    uint8_t gray_digital;       // 8位数字量
    uint8_t gray_analog[8];     // 8路模拟量
    uint32_t gray_timestamp;    // 灰度数据时间戳
    
    // 数据有效性
    bool angle_valid;           // 角度数据有效
    bool gray_valid;            // 灰度数据有效
} SensorData_t;

// 控制数据结构
typedef struct {
    // 控制目标
    float target_angle;         // 目标角度
    LineFollowing_Mode_t mode;  // 控制模式
    uint8_t base_speed;         // 基础速度
    
    // 控制状态
    float angle_error;          // 角度误差
    float line_error;           // 线路误差
    float angle_output;         // 角度控制输出
    float line_output;          // 巡线控制输出
    
    // 电机状态
    int8_t left_motor_speed;    // 左电机速度
    int8_t right_motor_speed;   // 右电机速度
    
    // 系统状态
    LineFollowing_State_t state; // 系统状态
    uint32_t update_count;      // 更新计数
    uint32_t last_update;       // 最后更新时间
} ControlData_t;

// 配置参数结构
typedef struct {
    // 角度控制参数
    float angle_kp, angle_ki, angle_kd;
    float angle_output_limit;
    
    // 巡线控制参数  
    float line_kp, line_ki, line_kd;
    float line_weights[8];
    float line_output_limit;
    
    // 电机控制参数
    uint8_t base_speed;
    float max_correction;
    
    // 系统参数
    LineFollowing_Mode_t mode;
    uint32_t update_period;
    bool debug_enabled;
} LineFollowing_Config_t;

// 调试数据结构
typedef struct {
    // 实时数据
    float current_yaw;
    float target_angle;
    float angle_error;
    float line_error;
    
    // 控制输出
    float angle_control_out;
    float line_control_out;
    int8_t left_motor_speed;
    int8_t right_motor_speed;
    
    // 系统状态
    LineFollowing_State_t system_state;
    uint32_t update_frequency;
    uint32_t error_count;
    
    // 传感器状态
    bool angle_sensor_ok;
    bool gray_sensor_ok;
    uint8_t gray_sensor_count;  // 检测到线的传感器数量
} DebugData_t;

/**********************************************************
*** 核心API接口
**********************************************************/

/**
  * @brief 初始化循迹系统
  * @param config: 配置参数 (可传NULL使用默认配置)
  * @retval HAL_OK: 初始化成功, HAL_ERROR: 初始化失败
  */
HAL_StatusTypeDef LineFollowing_Init(LineFollowing_Config_t* config);

/**
  * @brief 循迹系统主循环更新 (在main循环中调用)
  * @retval None
  * @note 此函数非阻塞，建议20Hz频率调用
  */
void LineFollowing_Update(void);

/**
  * @brief 启动循迹功能
  * @retval None
  */
void LineFollowing_Start(void);

/**
  * @brief 停止循迹功能
  * @retval None
  */
void LineFollowing_Stop(void);

/**
  * @brief 设定目标角度
  * @param target_angle: 目标角度 (-180° ~ +180°)
  * @retval None
  */
void LineFollowing_SetTargetAngle(float target_angle);

/**
  * @brief 设定工作模式
  * @param mode: 工作模式
  * @retval None
  */
void LineFollowing_SetMode(LineFollowing_Mode_t mode);

/**
  * @brief 设定基础速度
  * @param speed: 基础速度 (0-100)
  * @retval None
  */
void LineFollowing_SetBaseSpeed(uint8_t speed);

/**********************************************************
*** 状态查询接口
**********************************************************/

/**
  * @brief 获取当前系统状态
  * @retval LineFollowing_State_t: 当前状态
  */
LineFollowing_State_t LineFollowing_GetState(void);

/**
  * @brief 获取控制数据
  * @param angle_error: 角度误差指针
  * @param line_error: 线路误差指针
  * @param left_speed: 左电机速度指针
  * @param right_speed: 右电机速度指针
  * @retval None
  */
void LineFollowing_GetControlData(float* angle_error, float* line_error, 
                                  int8_t* left_speed, int8_t* right_speed);

/**
  * @brief 获取调试数据
  * @param debug_data: 调试数据结构指针
  * @retval None
  */
void LineFollowing_GetDebugData(DebugData_t* debug_data);

/**
  * @brief 获取状态字符串 (用于显示)
  * @retval const char*: 状态字符串
  */
const char* LineFollowing_GetStateString(void);

/**********************************************************
*** 配置接口
**********************************************************/

/**
  * @brief 设置角度PID参数
  * @param kp, ki, kd: PID参数
  * @retval None
  */
void LineFollowing_SetAnglePID(float kp, float ki, float kd);

/**
  * @brief 设置巡线PID参数
  * @param kp, ki, kd: PID参数
  * @retval None
  */
void LineFollowing_SetLinePID(float kp, float ki, float kd);

/**
  * @brief 设置传感器权重
  * @param weights: 8个传感器权重数组
  * @retval None
  */
void LineFollowing_SetSensorWeights(float weights[8]);

/**
  * @brief 重置控制器状态
  * @retval None
  */
void LineFollowing_Reset(void);

/**********************************************************
*** OLED显示集成接口
**********************************************************/

/**
  * @brief OLED显示循迹状态
  * @retval None
  */
void LineFollowing_DisplayStatus(void);

/**********************************************************
*** 内部函数声明 (用户无需调用)
**********************************************************/
HAL_StatusTypeDef SensorFusion_GetData(SensorData_t* sensor_data);
SensorError_t SensorFusion_CheckHealth(void);
float AngleControl_Update(AngleControl_t* ctrl, float target, float current);
float LineControl_Update(LineControl_t* ctrl, uint8_t digital, uint8_t* analog);
void MotorControl_Update(MotorControl_t* ctrl);
void MotorControl_SetInputs(MotorControl_t* ctrl, float angle_input, float line_input);
void AngleControl_Reset(AngleControl_t* ctrl);
void LineControl_Reset(LineControl_t* ctrl);

#endif // __LINE_FOLLOWING_H
