# 轻量级K230D红色追踪系统 PRD V2.0

## 文档信息
- **版本**: V2.0 - 轻量级重构版
- **负责人**: Emma (产品经理)
- **日期**: 2025-07-29
- **项目**: 高性能非阻塞K230D红色追踪系统

## 1. 问题分析

### 1.1 原系统问题
- ❌ **系统卡死**: 复杂的PID计算阻塞主循环
- ❌ **OLED无法刷新**: 长时间占用CPU导致显示卡顿
- ❌ **UART阻塞**: 同步通信方式影响系统响应
- ❌ **资源浪费**: 过度复杂的算法消耗过多资源

### 1.2 设计目标
- ✅ **非阻塞设计**: 所有操作都不能阻塞主循环
- ✅ **时间片管理**: 合理分配CPU时间给各个任务
- ✅ **轻量级算法**: 简化控制算法，减少计算量
- ✅ **异步通信**: 使用DMA和中断，避免阻塞等待

## 2. 轻量级系统设计

### 2.1 系统架构
```
主循环 (1ms) 
├── OLED刷新 (每100ms)
├── K230D数据处理 (每10ms)
├── 简化追踪控制 (每50ms)
└── 状态更新 (每200ms)
```

### 2.2 核心原则
1. **时间片原则**: 每个任务最多占用1ms CPU时间
2. **状态机原则**: 使用状态机避免复杂逻辑
3. **缓存原则**: 预计算常用数值，避免重复计算
4. **优先级原则**: OLED显示 > 数据接收 > 追踪控制

## 3. 功能规格

### 3.1 简化追踪算法
#### 替代复杂PID控制
```c
// 简单比例控制 (替代PID)
int16_t error_x = target_x - 320;  // 图像中心偏差
int16_t error_y = target_y - 240;

// 简单死区和比例控制
if (abs(error_x) > 10) {
    motor_speed_x = error_x / 4;  // 简单比例控制
} else {
    motor_speed_x = 0;  // 死区内停止
}
```

#### 非阻塞电机控制
```c
// 异步电机控制 (不等待响应)
void SendMotorCommand_NonBlocking(int16_t speed_x, int16_t speed_y) {
    static uint32_t last_send = 0;
    if (HAL_GetTick() - last_send > 50) {  // 限制发送频率
        // 发送命令但不等待响应
        HAL_UART_Transmit_DMA(&huart2, cmd_buffer, cmd_length);
        last_send = HAL_GetTick();
    }
}
```

### 3.2 时间片任务调度
```c
void Lightweight_Task_Scheduler(void) {
    uint32_t current_time = HAL_GetTick();
    
    // 任务1: OLED刷新 (最高优先级)
    if (current_time - last_oled_update > 100) {
        OLED_Update_Fast();  // 快速更新，<1ms
        last_oled_update = current_time;
    }
    
    // 任务2: K230D数据处理
    if (current_time - last_data_process > 10) {
        K230D_Process_Fast();  // 快速处理，<1ms
        last_data_process = current_time;
    }
    
    // 任务3: 追踪控制
    if (current_time - last_tracking_update > 50) {
        Simple_Tracking_Control();  // 简化控制，<1ms
        last_tracking_update = current_time;
    }
}
```

### 3.3 状态机设计
```c
typedef enum {
    TRACK_IDLE = 0,     // 空闲状态
    TRACK_SEARCH,       // 搜索状态  
    TRACK_ACTIVE,       // 追踪状态
    TRACK_LOST          // 丢失状态
} TrackingState_t;

void Simple_State_Machine(void) {
    switch (tracking_state) {
        case TRACK_IDLE:
            // 等待数据，最小CPU占用
            break;
        case TRACK_SEARCH:
            // 搜索模式，慢速扫描
            break;
        case TRACK_ACTIVE:
            // 追踪模式，快速响应
            break;
        case TRACK_LOST:
            // 丢失模式，停止电机
            break;
    }
}
```

## 4. 性能优化策略

### 4.1 计算优化
- **查表法**: 预计算三角函数和开方运算
- **整数运算**: 避免浮点运算，使用定点数
- **位运算**: 使用位移代替除法运算
- **缓存结果**: 缓存重复计算的结果

### 4.2 通信优化
- **DMA传输**: 所有UART通信使用DMA
- **命令缓存**: 预构建命令包，避免实时构建
- **发送限流**: 限制命令发送频率，避免总线拥塞
- **异步处理**: 发送命令后立即返回，不等待响应

### 4.3 内存优化
- **静态分配**: 避免动态内存分配
- **循环缓冲**: 使用循环缓冲区管理数据
- **内存池**: 预分配固定大小的内存块
- **栈优化**: 减少局部变量，避免栈溢出

## 5. 实现方案

### 5.1 核心数据结构
```c
typedef struct {
    // 目标坐标 (简化)
    int16_t target_x, target_y;
    uint32_t last_update;
    bool valid;
    
    // 控制输出 (简化)
    int16_t motor_x, motor_y;
    
    // 状态管理
    TrackingState_t state;
    uint32_t state_timer;
    
    // 统计信息 (简化)
    uint16_t valid_count;
    uint16_t lost_count;
} LightweightTracker_t;
```

### 5.2 主要函数
```c
void Lightweight_Tracker_Init(void);           // 初始化
void Lightweight_Tracker_Update(void);         // 主更新函数
void Simple_Tracking_Control(void);            // 简化追踪控制
void Fast_OLED_Display(void);                  // 快速OLED显示
void NonBlocking_Motor_Control(void);          // 非阻塞电机控制
```

## 6. 性能指标

### 6.1 时间要求
- **主循环周期**: 1ms (1000Hz)
- **OLED刷新**: 100ms (10Hz)
- **追踪控制**: 50ms (20Hz)
- **数据处理**: 10ms (100Hz)

### 6.2 CPU占用
- **OLED显示**: <5% CPU时间
- **数据处理**: <10% CPU时间
- **追踪控制**: <15% CPU时间
- **系统余量**: >70% CPU时间

### 6.3 响应性能
- **按键响应**: <10ms
- **显示更新**: <100ms
- **追踪响应**: <50ms
- **系统稳定**: 无卡死现象

## 7. 测试验证

### 7.1 性能测试
- **CPU占用率测试**: 使用示波器测量GPIO翻转
- **响应时间测试**: 测量各任务执行时间
- **稳定性测试**: 长时间运行无卡死
- **实时性测试**: OLED刷新流畅度

### 7.2 功能测试
- **追踪精度**: 目标跟踪准确性
- **状态切换**: 各状态间正确切换
- **异常恢复**: 数据丢失后自动恢复
- **用户体验**: 操作响应流畅

## 8. 实施计划

### 8.1 开发阶段
1. **移除原有系统** (10分钟): 清理复杂的PID代码
2. **实现轻量级核心** (15分钟): 基础追踪算法
3. **集成时间片调度** (10分钟): 任务调度器
4. **优化显示系统** (10分钟): 快速OLED更新
5. **测试和调优** (15分钟): 性能测试和参数调整

### 8.2 成功标准
- ✅ 系统运行流畅，无卡死现象
- ✅ OLED显示实时更新，无卡顿
- ✅ 追踪功能正常，响应及时
- ✅ CPU占用率<30%，系统余量充足
