--cpu=Cortex-M4.fp.sp
"f407 base\startup_stm32f407xx.o"
"f407 base\main.o"
"f407 base\stm32f4xx_it.o"
"f407 base\stm32f4xx_hal_msp.o"
"f407 base\stm32f4xx_hal_i2c.o"
"f407 base\stm32f4xx_hal_i2c_ex.o"
"f407 base\stm32f4xx_hal_rcc.o"
"f407 base\stm32f4xx_hal_rcc_ex.o"
"f407 base\stm32f4xx_hal_flash.o"
"f407 base\stm32f4xx_hal_flash_ex.o"
"f407 base\stm32f4xx_hal_flash_ramfunc.o"
"f407 base\stm32f4xx_hal_gpio.o"
"f407 base\stm32f4xx_hal_dma_ex.o"
"f407 base\stm32f4xx_hal_dma.o"
"f407 base\stm32f4xx_hal_pwr.o"
"f407 base\stm32f4xx_hal_pwr_ex.o"
"f407 base\stm32f4xx_hal_cortex.o"
"f407 base\stm32f4xx_hal.o"
"f407 base\stm32f4xx_hal_exti.o"
"f407 base\stm32f4xx_hal_tim.o"
"f407 base\stm32f4xx_hal_tim_ex.o"
"f407 base\stm32f4xx_hal_uart.o"
"f407 base\oled.o"
"f407 base\jy901s.o"
"f407 base\software_iic.o"
"f407 base\motor_driver.o"
"f407 base\emm_v5.o"
"f407 base\emmv5_pid.o"
"f407 base\k230d_getdata.o"
"f407 base\zhuizong.o"
"f407 base\testmodule.o"
"f407 base\system_stm32f4xx.o"
--strict --scatter "F407 base\F407 base.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "F407 base.map" -o "F407 base\F407 base.axf"