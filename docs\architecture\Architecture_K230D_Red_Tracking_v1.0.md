# K230D红色追踪云台系统架构设计

## 文档信息
- **版本**: V1.0
- **负责人**: Bob (架构师)
- **日期**: 2025-07-29
- **项目**: K230D坐标驱动EMMV5二维云台红色目标追踪系统

## 1. 系统总体架构

### 1.1 架构概览
```
┌─────────────────┐    UART1     ┌─────────────────────────────────┐
│   K230D开发板    │   115200     │        STM32F407VET6           │
│                │ ──────────→  │                                │
│ • 红色目标检测   │              │ • 坐标数据接收与解析             │
│ • 坐标计算      │              │ • PID控制算法                  │
│ • 数据传输      │              │ • 双轴云台控制                 │
│ • 30Hz更新频率  │              │ • 状态监控与显示               │
└─────────────────┘              └─────────────────────────────────┘
                                           │
                                    UART2 & UART4
                                           │
                                           ▼
                                 ┌─────────────────┐
                                 │  EMMV5双轴云台   │
                                 │                │
                                 │ • X轴步进电机    │
                                 │ • Y轴步进电机    │
                                 │ • 20Hz控制频率   │
                                 └─────────────────┘
```

### 1.2 数据流架构
```
K230D检测 → 坐标数据 → UART传输 → STM32接收 → 数据解析 → PID计算 → 电机控制 → 云台运动
    ↑                                                                      ↓
    └──────────────────── 反馈控制 ←─────────────────────────────────────────┘
```

## 2. 核心模块设计

### 2.1 K230D红色检测模块
**文件**: `K230D_Red_Tracking_System.py`
**功能**: 红色目标检测和坐标数据传输

#### 核心组件
- **图像采集**: Sensor模块，640x480分辨率
- **颜色检测**: HSV阈值红色检测
- **坐标计算**: 目标质心坐标计算
- **数据传输**: UART1数据包传输

#### 关键参数
```python
RED_THRESHOLD = [(0, 80, -70, 70, 30, 127)]  # 红色HSV阈值
MIN_BLOB_AREA = 200                          # 最小检测面积
SEND_INTERVAL = 33                           # 30Hz发送间隔
DATA_FORMAT = "RED_CENTER:X={x},Y={y}\n"     # 数据包格式
```

### 2.2 STM32数据接收模块
**文件**: `Moudle/K230D_getData/K230D_getData.c`
**功能**: 接收和解析K230D坐标数据

#### 核心组件
- **DMA接收**: USART6 DMA环形缓冲区接收
- **数据解析**: 正则表达式解析坐标数据
- **异常处理**: 超时检测和数据验证
- **状态管理**: 数据更新标志和统计信息

#### 关键函数
```c
void K230D_ProcessData(void);                    // 数据处理主函数
uint8_t K230D_GetCoordinates(int16_t *x, int16_t *y);  // 获取坐标
uint32_t K230D_GetDataAge(void);                 // 获取数据年龄
void K230D_ClearUpdateFlag(void);                // 清除更新标志
```

### 2.3 PID控制模块
**文件**: `Moudle/EMMV5_PID/EMMV5_PID.c`
**功能**: 双轴独立PID控制算法

#### 核心组件
- **双PID控制器**: X轴和Y轴独立PID
- **参数配置**: 可调节Kp、Ki、Kd参数
- **输出限制**: 速度限制和死区处理
- **目标管理**: 目标坐标设置和跟踪

#### PID参数设计
```c
// 推荐PID参数
#define PID_KP_DEFAULT    2.0f    // 比例系数
#define PID_KI_DEFAULT    0.1f    // 积分系数  
#define PID_KD_DEFAULT    0.5f    // 微分系数
#define IMAGE_CENTER_X    320     // 图像中心X
#define IMAGE_CENTER_Y    240     // 图像中心Y
```

### 2.4 云台控制模块
**文件**: `Moudle/EMMV5_PID/EMMV5_PID.c`
**功能**: EMMV5双轴步进云台精确控制

#### 核心组件
- **双电机控制**: X轴(UART2)和Y轴(UART4)
- **速度控制**: 基于PID输出的速度映射
- **安全保护**: 速度限制和紧急停止
- **状态监控**: 电机状态和错误检测

#### 控制参数
```c
#define MOTOR_MAX_SPEED      1000   // 最大电机速度
#define MOTOR_MIN_SPEED      50     // 最小电机速度
#define MOTOR_DEAD_ZONE      10     // 电机死区
#define MOTOR_ACCELERATION   200    // 电机加速度
```

## 3. 通信协议设计

### 3.1 K230D → STM32 通信
**接口**: UART1(K230D) → USART6(STM32)
**参数**: 115200-8N1
**格式**: `RED_CENTER:X=123,Y=456\n`

#### 数据包结构
```
起始标识: "RED_CENTER:"
X坐标: "X=123"
分隔符: ","
Y坐标: "Y=456"
结束符: "\n"
```

### 3.2 STM32 → EMMV5 通信
**接口**: UART2(X轴), UART4(Y轴)
**参数**: 9600-8N1
**协议**: EMMV5标准协议

#### 控制指令
```c
// 速度控制指令
Emm_V5_Vel_Control(huart, addr, dir, speed, acc, false);
// 停止指令  
Emm_V5_Stop_Now(huart, addr, false);
```

## 4. 状态管理设计

### 4.1 系统状态定义
```c
typedef enum {
    TRACKING_IDLE = 0,      // 空闲状态
    TRACKING_SEARCH,        // 搜索状态
    TRACKING_ACTIVE,        // 追踪状态
    TRACKING_LOST,          // 目标丢失
    TRACKING_ERROR          // 错误状态
} TrackingState_t;
```

### 4.2 状态转换逻辑
```
IDLE → SEARCH → ACTIVE → LOST → SEARCH
  ↓       ↓       ↓       ↓       ↓
  └─────→ ERROR ←─────────────────┘
```

## 5. 性能指标设计

### 5.1 时序要求
- **K230D检测频率**: 30Hz (33ms周期)
- **数据传输延迟**: <10ms
- **STM32处理延迟**: <5ms
- **PID控制频率**: 20Hz (50ms周期)
- **总系统延迟**: <100ms

### 5.2 精度要求
- **追踪精度**: ±5像素
- **坐标范围**: X[0-640], Y[0-480]
- **电机精度**: 0.1度步进角
- **控制死区**: ±10像素

## 6. 异常处理设计

### 6.1 数据异常处理
- **数据超时**: 200ms无数据自动停止追踪
- **坐标越界**: 超出范围的坐标自动丢弃
- **格式错误**: 解析失败的数据包忽略
- **通信中断**: 自动重连和恢复机制

### 6.2 硬件异常处理
- **电机过载**: 自动降速和保护
- **通信故障**: 错误检测和重试机制
- **系统过热**: 温度监控和保护
- **电源异常**: 低电压检测和报警

## 7. 调试与监控设计

### 7.1 OLED显示内容
```
行1: "K230D Tracking"     (系统标题)
行2: "Status: ACTIVE"     (追踪状态)
行3: "Target: X=320 Y=240" (目标坐标)
行4: "Valid:123 Lost:5"   (统计信息)
```

### 7.2 串口调试信息
```c
printf("Target: X=%d, Y=%d\r\n", x, y);           // 目标坐标
printf("PID Output: X=%.2f, Y=%.2f\r\n", x_out, y_out);  // PID输出
printf("Motor Speed: X=%d, Y=%d\r\n", x_speed, y_speed); // 电机速度
printf("Data Age: %lums\r\n", data_age);          // 数据年龄
```

## 8. 部署架构

### 8.1 硬件连接
```
K230D Pin40(TX) → STM32 PC7(USART6_RX)
K230D Pin41(RX) → STM32 PC6(USART6_TX)
STM32 PA2(TX) → EMMV5 X轴电机
STM32 PA3(RX) ← EMMV5 X轴电机
STM32 PA0(TX) → EMMV5 Y轴电机  
STM32 PA1(RX) ← EMMV5 Y轴电机
```

### 8.2 软件部署
1. **K230D端**: 烧录红色检测程序
2. **STM32端**: 编译并下载追踪控制程序
3. **参数配置**: 调整PID参数和检测阈值
4. **系统测试**: 验证追踪精度和响应速度
