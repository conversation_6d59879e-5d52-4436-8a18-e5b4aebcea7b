# K230D开发板完整学习指南

## 概述
K230D是正点原子推出的开发板，使用CanMV版Python编程，基于Kendryte K230D芯片。

## 核心特征

### 1. FPIOA（功能引脚分配器）- K230D的核心特色
**这是K230D区别于其他开发板的关键特征！**

```python
from machine import FPIOA

# 实例化FPIOA
fpioa = FPIOA()

# 所有引脚使用前必须先分配功能
fpioa.set_function(59, FPIOA.GPIO59)  # 设置Pin59为GPIO59
fpioa.set_function(60, FPIOA.PWM0)    # 设置Pin60为PWM0
fpioa.set_function(40, FPIOA.UART1_TXD)  # 设置Pin40为UART1发送
```

### 2. 核心库导入
```python
# 基础硬件控制
from machine import Pin, PWM, UART, FPIOA
import time

# 媒体处理（摄像头、显示）
from media.sensor import *    # 摄像头相关接口
from media.display import *   # 显示相关接口  
from media.media import *     # 媒体资源管理

# AI推理
from libs.PipeLine import PipeLine, ScopedTiming
from libs.AIBase import AIBase
from libs.AI2D import Ai2d
import nncase_runtime as nn
import ulab.numpy as np
```

## 硬件接口详解

### 1. GPIO控制
```python
# FPIOA分配 + Pin对象创建
fpioa.set_function(59, FPIOA.GPIO59)
led = Pin(59, Pin.OUT, pull=Pin.PULL_NONE, drive=7)

# GPIO操作
led.value(1)  # 输出高电平
led.value(0)  # 输出低电平
# 或者使用：led.on(), led.off(), led.high(), led.low()

# 输入引脚
fpioa.set_function(34, FPIOA.GPIO34)
key = Pin(34, Pin.IN, pull=Pin.PULL_UP, drive=7)
state = key.value()  # 读取引脚状态
```

### 2. PWM控制
```python
# FPIOA分配PWM功能
fpioa.set_function(60, FPIOA.PWM0)

# 创建PWM对象：PWM(通道, 频率, 占空比, 使能)
pwm = PWM(0, 4000, duty=50, enable=True)

# PWM控制
pwm.enable(True)   # 启用PWM
pwm.enable(False)  # 禁用PWM
```

### 3. UART通信
```python
# FPIOA分配UART功能
fpioa.set_function(40, FPIOA.UART1_TXD)
fpioa.set_function(41, FPIOA.UART1_RXD)

# 创建UART对象
uart = UART(UART.UART1, baudrate=115200, bits=UART.EIGHTBITS, 
           parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)

# UART操作
uart.write("Hello K230D!")  # 发送数据
data = uart.read(128)       # 接收数据
if data != None:
    print("收到:", data.decode())
```

## 摄像头与显示系统

### 摄像头初始化标准流程（顺序不能颠倒！）
```python
# 1. 构建摄像头对象
sensor = Sensor(width=1280, height=960)

# 2. 复位和初始化
sensor.reset()

# 3. 设置帧大小和像素格式
sensor.set_framesize(Sensor.VGA)      # VGA(640x480)
sensor.set_pixformat(Sensor.RGB565)   # 或 Sensor.YUV420SP

# 4. 初始化显示器
Display.init(Display.ST7701, width=640, height=480, fps=90, to_ide=True)

# 5. 初始化媒体管理器
MediaManager.init()

# 6. 启动摄像头
sensor.run()

# 7. 图像捕获和处理
while True:
    img = sensor.snapshot()  # 捕获图像
    Display.show_image(img)  # 显示图像
```

### 资源释放（重要！）
```python
try:
    # 主程序循环
    while True:
        os.exitpoint()  # 检测IDE中断
        # 处理逻辑...
        
except KeyboardInterrupt as e:
    print("用户停止:", e)
finally:
    # 必须按顺序释放资源
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
```

## 图像处理功能

### 颜色识别
```python
# LAB颜色空间阈值 (l_lo, l_hi, a_lo, a_hi, b_lo, b_hi)
thresholds = [(0, 80, 40, 80, 10, 80),    # 红色
              (0, 80, -120, -10, 0, 30),   # 绿色  
              (0, 80, 0, 90, -128, -20)]   # 蓝色

# 颜色块检测
blobs = img.find_blobs([thresholds[0]], pixels_threshold=200)
for blob in blobs:
    img.draw_rectangle(blob[0], blob[1], blob[2], blob[3], 
                      color=(255, 0, 0), thickness=4)
```

## AI推理架构

### AI应用标准架构
```python
# 1. 继承AIBase基类
class FaceDetectionApp(AIBase):
    def __init__(self, kmodel_path, model_input_size, ...):
        super().__init__(kmodel_path, model_input_size, rgb888p_size, debug_mode)
        self.ai2d = Ai2d(debug_mode)  # AI2D预处理
        
    # 2. 配置预处理
    def config_preprocess(self, input_image_size=None):
        # 使用AI2D进行预处理：pad, resize, crop等
        self.ai2d.pad([0, 0, 0, 0, top, bottom, left, right], 0, [104, 117, 123])
        self.ai2d.resize(nn.interp_method.tf_bilinear, nn.interp_mode.half_pixel)
        self.ai2d.build([1,3,h,w], [1,3,model_h,model_w])
        
    # 3. 自定义后处理
    def postprocess(self, results):
        # 处理模型输出，返回检测结果
        return processed_results
        
    # 4. 绘制结果
    def draw_result(self, pl, dets):
        # 在图像上绘制检测框等结果
        pl.osd_img.draw_rectangle(x, y, w, h, color=(255, 255, 0, 255))

# 5. 使用PipeLine管道
pl = PipeLine(rgb888p_size=[640, 480], display_size=[640, 480], display_mode=0)
ai_app = FaceDetectionApp(kmodel_path, model_input_size, ...)
ai_app.config_preprocess()

while True:
    img = pl.get_frame()        # 获取帧
    res = ai_app.run(img)       # AI推理
    ai_app.draw_result(pl, res) # 绘制结果
    pl.show_image()             # 显示
```

## 重要注意事项

1. **FPIOA是必须的**：所有引脚使用前都必须通过fpioa.set_function()分配功能
2. **摄像头初始化顺序**：sensor.reset() → set_framesize() → set_pixformat() → Display.init() → MediaManager.init() → sensor.run()
3. **资源管理**：必须正确释放sensor、Display、MediaManager资源
4. **AI2D预处理**：K230D的AI推理需要使用AI2D进行图像预处理
5. **PipeLine管道**：AI应用推荐使用PipeLine管道架构

## 支持的AI功能
- 人脸检测、人脸识别、人脸关键点检测
- 人体检测、人体关键点检测、跌倒检测  
- 手掌检测、手势识别、动态手势识别
- 物体检测、物体分割
- 车牌检测、车牌号识别
- 关键词唤醒、自分类学习

## 常用代码模板

### 基础GPIO控制模板
```python
from machine import Pin, FPIOA
import time

# 初始化FPIOA
fpioa = FPIOA()

# LED控制
fpioa.set_function(59, FPIOA.GPIO59)  # 蓝色LED
fpioa.set_function(61, FPIOA.GPIO61)  # 红色LED
ledb = Pin(59, Pin.OUT, pull=Pin.PULL_NONE, drive=7)
ledr = Pin(61, Pin.OUT, pull=Pin.PULL_NONE, drive=7)

# 按键输入
fpioa.set_function(34, FPIOA.GPIO34)
key = Pin(34, Pin.IN, pull=Pin.PULL_UP, drive=7)

while True:
    if key.value() == 0:  # 按键按下
        ledr.value(0)     # 点亮红色LED
    else:
        ledr.value(1)     # 熄灭红色LED
```

### 摄像头基础模板
```python
import time, os, sys
from media.sensor import *
from media.display import *
from media.media import *

try:
    # 摄像头初始化
    sensor = Sensor(width=1280, height=960)
    sensor.reset()
    sensor.set_framesize(Sensor.VGA)
    sensor.set_pixformat(Sensor.RGB565)

    # 显示初始化
    Display.init(Display.ST7701, width=640, height=480, fps=90, to_ide=True)
    MediaManager.init()
    sensor.run()

    while True:
        os.exitpoint()
        img = sensor.snapshot()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止:", e)
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
```

### AI推理基础模板
```python
from libs.PipeLine import PipeLine, ScopedTiming
from libs.AIBase import AIBase
from libs.AI2D import Ai2d
import os, gc, sys

class MyAIApp(AIBase):
    def __init__(self, kmodel_path, model_input_size, rgb888p_size=[640,480], display_size=[640,480]):
        super().__init__(kmodel_path, model_input_size, rgb888p_size, debug_mode=0)
        self.ai2d = Ai2d(debug_mode=0)
        self.ai2d.set_ai2d_dtype(nn.ai2d_format.NCHW_FMT, nn.ai2d_format.NCHW_FMT, np.uint8, np.uint8)

    def config_preprocess(self, input_image_size=None):
        with ScopedTiming("set preprocess config", False):
            ai2d_input_size = input_image_size if input_image_size else self.rgb888p_size
            self.ai2d.resize(nn.interp_method.tf_bilinear, nn.interp_mode.half_pixel)
            self.ai2d.build([1,3,ai2d_input_size[1],ai2d_input_size[0]],
                           [1,3,self.model_input_size[1],self.model_input_size[0]])

    def postprocess(self, results):
        # 自定义后处理逻辑
        return results

    def draw_result(self, pl, results):
        # 自定义结果绘制
        if results:
            pl.osd_img.clear()
            # 绘制检测结果...

# 使用示例
if __name__ == "__main__":
    kmodel_path = "/sdcard/app/models/your_model.kmodel"
    pl = PipeLine(rgb888p_size=[640, 480], display_size=[640, 480], display_mode=0)
    ai_app = MyAIApp(kmodel_path, model_input_size=[320, 320])
    ai_app.config_preprocess()

    try:
        while True:
            os.exitpoint()
            with ScopedTiming("total", 1):
                img = pl.get_frame()
                res = ai_app.run(img)
                ai_app.draw_result(pl, res)
                pl.show_image()
                gc.collect()
    except Exception as e:
        sys.print_exception(e)
    finally:
        ai_app.deinit()
        pl.destroy()
```

## 硬件引脚定义（K230D BOX）
```python
# LED引脚
LED_BLUE = 59   # 蓝色LED
LED_RED = 61    # 红色LED

# 按键引脚
KEY0 = 34       # 按键0
KEY1 = 35       # 按键1
KEY2 = 0        # 按键2（BOOT按键）

# 蜂鸣器
BEEP = 60       # 蜂鸣器PWM

# UART引脚
UART1_TX = 40
UART1_RX = 41
UART2_TX = 44
UART2_RX = 45
```

## 开发环境
- 编程语言：CanMV版Python
- 开发工具：CanMV IDE
- 芯片：Kendryte K230D
- 核心特色：FPIOA功能引脚分配器

## 学习资源
- 正点原子官网：www.alientek.com
- 技术论坛：www.openedv.com
- 在线视频：www.yuanzige.com
- 购买地址：openedv.taobao.com
