# K230D红色追踪二维云台控制系统 PRD

## 文档信息
- **版本**: V1.0
- **负责人**: Emma (产品经理)
- **日期**: 2025-07-29
- **项目**: K230D坐标驱动EMMV5二维云台红色目标追踪系统

## 1. 背景与问题陈述

### 1.1 项目背景
用户需要实现基于K230D开发板的红色目标追踪系统，通过K230D进行红色目标检测并获取坐标数据，然后传输给STM32F407控制EMMV5二维步进云台进行精确追踪。

### 1.2 核心问题
- **数据传输**: K230D检测到的红色目标坐标需要实时传输给STM32
- **云台控制**: STM32需要根据坐标数据精确控制EMMV5双轴步进云台
- **追踪精度**: 系统需要实现高精度、低延迟的目标追踪
- **异常处理**: 需要处理目标丢失、数据超时等异常情况

## 2. 目标与成功指标

### 2.1 项目目标 (Objectives)
1. **实时追踪**: 实现30Hz数据接收，20Hz控制更新的实时追踪
2. **高精度控制**: 追踪精度达到±5像素，系统延迟<100ms
3. **稳定运行**: 系统能够连续稳定运行，具备完善的异常处理机制
4. **状态监控**: 提供完整的系统状态显示和调试信息

### 2.2 关键结果 (Key Results)
- **追踪精度**: ±5像素内的目标跟踪精度
- **响应延迟**: 从目标检测到云台响应<100ms
- **数据传输**: 30Hz稳定的坐标数据传输
- **控制频率**: 20Hz的PID控制更新频率
- **异常恢复**: 目标丢失后2秒内自动恢复追踪

### 2.3 反向指标 (Counter Metrics)
- **系统延迟**: 不超过100ms
- **追踪误差**: 不超过±10像素
- **数据丢失率**: 不超过5%

## 3. 用户画像与用户故事

### 3.1 目标用户
- **技术开发者**: 需要实现自动追踪系统的工程师
- **研究人员**: 进行视觉追踪算法研究的科研人员
- **教育用户**: 学习嵌入式视觉系统的学生

### 3.2 用户故事
- **作为开发者**, 我希望系统能够自动追踪红色目标，这样我就可以实现自动化的目标跟踪应用
- **作为用户**, 我希望看到实时的追踪状态和坐标信息，这样我就可以监控系统运行状态
- **作为调试者**, 我希望有详细的调试信息输出，这样我就可以快速定位和解决问题

## 4. 功能规格详述

### 4.1 K230D端功能
#### 4.1.1 红色目标检测
- **颜色阈值**: 可配置的红色HSV阈值范围
- **目标筛选**: 基于面积和像素数的目标筛选
- **中心计算**: 计算检测目标的质心坐标

#### 4.1.2 数据传输
- **传输协议**: UART1, 115200波特率
- **数据格式**: "RED_CENTER:X=123,Y=456\n"
- **传输频率**: 30Hz (每33ms发送一次)
- **状态指示**: LED指示数据发送状态

### 4.2 STM32F407端功能
#### 4.2.1 数据接收处理
- **UART接收**: USART6 DMA接收K230D数据
- **数据解析**: 解析坐标数据格式
- **数据验证**: 坐标范围验证和数据有效性检查
- **超时检测**: 数据超时检测和异常处理

#### 4.2.2 PID控制系统
- **双轴独立控制**: X轴和Y轴独立的PID控制器
- **参数配置**: 可调节的Kp、Ki、Kd参数
- **控制频率**: 20Hz控制更新频率
- **死区处理**: 电机死区补偿

#### 4.2.3 EMMV5云台控制
- **双电机控制**: X轴(UART2)和Y轴(UART4)电机控制
- **速度控制**: 基于PID输出的电机速度控制
- **安全保护**: 电机过载保护和紧急停止

### 4.3 显示与监控
#### 4.3.1 OLED显示
- **实时状态**: 显示追踪状态(ACTIVE/SEARCH/IDLE)
- **坐标信息**: 显示当前目标坐标
- **统计信息**: 显示有效坐标数和丢失次数

#### 4.3.2 串口调试
- **详细日志**: 完整的系统运行日志
- **性能统计**: FPS、延迟、错误率等统计信息
- **调试模式**: 可开关的详细调试信息

## 5. 范围定义

### 5.1 包含功能 (In Scope)
- ✅ K230D红色目标检测和坐标获取
- ✅ UART数据传输和解析
- ✅ 双轴PID控制算法
- ✅ EMMV5步进电机云台控制
- ✅ OLED实时状态显示
- ✅ 串口调试和监控
- ✅ 异常处理和错误恢复

### 5.2 排除功能 (Out of Scope)
- ❌ 多目标追踪
- ❌ 其他颜色目标检测
- ❌ 网络通信功能
- ❌ 图像存储和回放
- ❌ 机器学习算法优化

## 6. 依赖与风险

### 6.1 内部依赖项
- **EMMV5_PID模块**: 完整的PID控制系统
- **K230D_getData模块**: 数据接收和解析
- **OLED显示模块**: 状态显示功能
- **UART通信模块**: 多路UART通信

### 6.2 外部依赖项
- **K230D开发板**: CanMV Python环境
- **EMMV5步进电机**: 双轴步进云台硬件
- **STM32F407**: 主控制器硬件平台

### 6.3 潜在风险
- **通信延迟**: UART通信可能存在延迟和丢包
- **电机响应**: 步进电机响应速度可能影响追踪效果
- **环境干扰**: 光照变化可能影响红色检测精度
- **系统稳定性**: 长时间运行的稳定性需要验证

## 7. 发布初步计划

### 7.1 开发阶段
1. **架构设计** (1天): 完成系统架构设计和模块划分
2. **K230D开发** (1天): 实现红色检测和数据传输
3. **STM32开发** (2天): 实现数据接收、PID控制和云台驱动
4. **集成测试** (1天): 系统集成和功能测试
5. **优化调试** (1天): 性能优化和参数调整

### 7.2 测试计划
- **单元测试**: 各模块独立功能测试
- **集成测试**: 系统整体功能测试
- **性能测试**: 追踪精度和响应延迟测试
- **稳定性测试**: 长时间运行稳定性测试

### 7.3 部署方案
- **灰度测试**: 先在实验环境进行功能验证
- **全量部署**: 确认功能正常后进行完整部署
- **监控跟踪**: 部署后持续监控系统运行状态
