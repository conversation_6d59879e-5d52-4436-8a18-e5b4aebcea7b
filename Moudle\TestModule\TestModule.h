#ifndef __TESTMODULE_H
#define __TESTMODULE_H

#include "main.h"
#include "stdbool.h"
#include "oled.h"
#include "software_iic.h"
#include "Emm_V5.h"
#include "jy901s.h"

/**********************************************************
***	STM32F407测试模块
***	编写作者：Alex (工程师)
***	功能：封装所有测试和辅助函数
***	版本：V1.0 - 模块化封装版
***	日期：2025-07-29
**********************************************************/

// 测试模块配置结构体
typedef struct {
    bool grayscale_enabled;        // 灰度传感器测试使能
    bool motor_test_enabled;       // 电机测试使能
    bool emmv5_test_enabled;       // EMMV5测试使能
    uint16_t test_interval_ms;     // 测试间隔时间
} TestModule_Config_t;

// 测试状态结构体
typedef struct {
    bool grayscale_connected;      // 灰度传感器连接状态
    bool emmv5_initialized;        // EMMV5初始化状态
    uint32_t test_counter;         // 测试计数器
    uint32_t last_test_time;       // 最后测试时间
} TestModule_Status_t;

/**********************************************************
*** 核心API接口
**********************************************************/

/**
  * @brief 初始化测试模块
  * @param config: 测试配置参数 (可传NULL使用默认配置)
  * @retval HAL_OK: 初始化成功, HAL_ERROR: 初始化失败
  */
HAL_StatusTypeDef TestModule_Init(TestModule_Config_t* config);

/**
  * @brief 测试模块主循环更新
  * @retval None
  * @note 此函数非阻塞，可高频率调用
  */
void TestModule_Update(void);

/**
  * @brief 启用/禁用特定测试
  * @param test_type: 测试类型 (0=灰度, 1=电机, 2=EMMV5)
  * @param enable: true=启用, false=禁用
  * @retval None
  */
void TestModule_EnableTest(uint8_t test_type, bool enable);

/**
  * @brief 获取测试状态
  * @retval TestModule_Status_t*: 测试状态指针
  */
TestModule_Status_t* TestModule_GetStatus(void);

/**********************************************************
*** 灰度传感器测试接口
**********************************************************/

/**
  * @brief 初始化灰度传感器
  * @retval HAL_OK: 成功, HAL_ERROR: 失败
  */
HAL_StatusTypeDef TestModule_GrayScale_Init(void);

/**
  * @brief 读取灰度传感器数据
  * @retval None
  */
void TestModule_GrayScale_ReadData(void);

/**
  * @brief 在OLED上显示灰度传感器数据
  * @param line: 显示行号 (0-7)
  * @retval None
  */
void TestModule_GrayScale_ShowData(uint8_t line);

/**
  * @brief 获取灰度传感器连接状态
  * @retval bool: true=已连接, false=未连接
  */
bool TestModule_GrayScale_IsConnected(void);

/**********************************************************
*** EMMV5电机测试接口
**********************************************************/

/**
  * @brief 初始化EMMV5双轴电机
  * @retval HAL_OK: 成功, HAL_ERROR: 失败
  */
HAL_StatusTypeDef TestModule_EMMV5_Init(void);

/**
  * @brief EMMV5电机测试更新
  * @retval None
  * @note 自动循环测试X轴和Y轴
  */
void TestModule_EMMV5_TestUpdate(void);

/**
  * @brief 在OLED上显示EMMV5电机状态
  * @param x: X坐标
  * @param y: Y坐标
  * @retval None
  */
void TestModule_EMMV5_ShowStatus(uint8_t x, uint8_t y);

/**
  * @brief 停止所有EMMV5电机
  * @retval None
  */
void TestModule_EMMV5_StopAll(void);

/**********************************************************
*** 辅助工具接口
**********************************************************/

/**
  * @brief 在OLED上显示带符号的角度
  * @param x: X坐标
  * @param y: Y坐标
  * @param angle: 角度值(-180 ~ +180)
  * @retval None
  */
void TestModule_OLED_ShowSignedAngle(uint8_t x, uint8_t y, int16_t angle);

/**
  * @brief I2C地址扫描
  * @param addr: 要扫描的I2C地址
  * @retval uint8_t: 0=有应答, 1=无应答
  */
uint8_t TestModule_I2C_ScanAddress(uint8_t addr);

/**
  * @brief 扫描所有I2C设备
  * @param found_devices: 存储找到的设备地址数组
  * @param max_devices: 数组最大长度
  * @retval uint8_t: 找到的设备数量
  */
uint8_t TestModule_I2C_ScanAllDevices(uint8_t* found_devices, uint8_t max_devices);

/**********************************************************
*** 调试接口
**********************************************************/

/**
  * @brief 启用/禁用调试模式
  * @param enable: true=启用, false=禁用
  * @retval None
  */
void TestModule_SetDebugMode(bool enable);

/**
  * @brief 重置测试计数器
  * @retval None
  */
void TestModule_ResetCounters(void);

/**
  * @brief 获取测试统计信息字符串
  * @param buffer: 字符串缓冲区
  * @param size: 缓冲区大小
  * @retval None
  */
void TestModule_GetStatsString(char* buffer, uint16_t size);

/**********************************************************
*** 内部函数声明 (用户无需调用)
**********************************************************/
void TestModule_ProcessGrayScale(void);
void TestModule_ProcessEMMV5Test(void);
void TestModule_UpdateCounters(void);

#endif // __TESTMODULE_H
