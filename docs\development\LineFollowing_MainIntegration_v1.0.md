# STM32F407循迹系统main.c集成测试文档

## 文档信息
- **文档名称**: 循迹系统main.c集成测试文档 (正确版本)
- **版本**: V1.1 - 正确路径集成版
- **创建日期**: 2025-07-29
- **作者**: <PERSON> (工程师) - 米醋电子工作室
- **文档类型**: 集成测试文档
- **集成路径**: `C:\Users\<USER>\Desktop\F407 TABLE TEST\F407 base\Core\Src\main.c`

---

## 集成概述

### 🎯 **集成目标**
将完整的循迹功能集成到正确的STM32F407主控制器main.c文件中，在现有TestModule基础上添加：
- JY901S陀螺仪角度控制 (UART5)
- 8路灰度传感器巡线控制 (I2C2软件IIC)
- DRV8871双电机驱动控制 (TIM1 PWM)
- OLED实时状态显示 (I2C1)
- 串口调试输出 (UART6)
- 与现有EMMV5云台系统兼容

### ✅ **集成完成状态**
- [x] 头文件引用完整集成
- [x] 全局变量定义完整
- [x] 硬件初始化函数完整
- [x] 循迹系统初始化完整
- [x] 主循环控制逻辑完整
- [x] UART中断处理完整
- [x] 错误处理机制完整

---

## 核心集成内容

### 1. **头文件集成**
```c
#include "../../Moudle/LineFollowing/line_following.h"
#include "../../Moudle/jy901s/jy901s.h"
#include "../../Moudle/grayscale/software_iic.h"
#include "../../Moudle/motor/motor_driver.h"
```

### 2. **硬件外设初始化**
- **I2C1**: OLED显示器通信
- **I2C2**: 8路灰度传感器通信
- **UART5**: JY901S陀螺仪数据接收 (9600波特率)
- **UART6**: 调试信息输出 (115200波特率)
- **TIM1**: 4路PWM电机控制 (1kHz频率)

### 3. **循迹系统配置**
```c
LineFollowing_Config_t config = {
    // 角度控制PID参数
    .angle_kp = 2.5f, .angle_ki = 0.1f, .angle_kd = 0.8f,
    .angle_output_limit = 40.0f,
    
    // 巡线控制PID参数  
    .line_kp = 1.8f, .line_ki = 0.05f, .line_kd = 0.4f,
    .line_output_limit = 35.0f,
    
    // 传感器权重 (外侧权重更大)
    .line_weights = {-4.0f, -2.5f, -1.2f, -0.6f, 0.6f, 1.2f, 2.5f, 4.0f},
    
    // 电机参数
    .base_speed = 45,      // 45%基础速度
    .max_correction = 25,  // 最大25%修正
    
    // 系统参数
    .mode = FUSION,        // 融合控制模式
    .update_period = 50,   // 50ms更新周期 (20Hz)
    .debug_enabled = true
};
```

---

## 主要功能函数

### 1. **系统初始化函数**
- `LineFollowing_System_Init()`: 完整系统初始化
- `Hardware_Modules_Init()`: 硬件模块初始化
- `JY901S_Module_Init()`: JY901S陀螺仪初始化
- `GrayScale_Module_Init()`: 灰度传感器初始化
- `Motor_Module_Init()`: 电机驱动初始化

### 2. **主循环控制函数**
- `LineFollowing_Main_Loop()`: 20Hz主控制循环
- `System_Status_Display()`: OLED状态显示 (5Hz)
- `System_Debug_Output()`: 串口调试输出 (1Hz)

### 3. **测试功能函数**
- `LineFollowing_System_Test()`: 完整功能测试
  - 纯角度控制模式测试 (5秒)
  - 纯巡线控制模式测试 (5秒)  
  - 融合控制模式测试 (10秒)

### 4. **中断处理函数**
- `HAL_UART_RxCpltCallback()`: UART5接收中断处理

---

## 控制流程

### 启动流程
1. **硬件初始化** (0-2秒)
   - 外设初始化 (GPIO/I2C/UART/TIM)
   - OLED显示器初始化
   - 传感器模块初始化

2. **系统初始化** (2-3秒)
   - 循迹功能初始化
   - 参数配置加载
   - 传感器数据预热

3. **控制启动** (3秒后)
   - 设置目标角度0° (直线行驶)
   - 启动循迹控制
   - 进入20Hz主控制循环

### 运行流程
```
主循环 (50ms周期)
├── LineFollowing_Update()     // 循迹控制更新
├── System_Status_Display()    // OLED显示更新 (200ms)
└── System_Debug_Output()      // 串口调试输出 (1000ms)
```

---

## 性能指标

### 实时性能
- **控制频率**: 20Hz (50ms周期)
- **显示更新**: 5Hz (200ms周期)
- **调试输出**: 1Hz (1000ms周期)

### 控制精度
- **角度控制精度**: ±2° (目标)
- **巡线响应时间**: <50ms (目标)
- **转弯控制精度**: ±5° (目标)

### 系统稳定性
- **传感器容错**: 支持单传感器故障继续运行
- **错误恢复**: 自动错误检测和安全停止
- **看门狗保护**: 系统异常自动重启

---

## 调试接口

### OLED显示内容
- 第1行: 系统状态和模式
- 第2行: 当前角度和目标角度
- 第3行: 线路误差和传感器状态
- 第4行: 左右电机速度

### 串口调试输出
```
=== 循迹系统状态 (运行时间: 120秒) ===
系统状态: RUNNING | 更新频率: 20Hz | 更新计数: 2400
目标角度: 0.0° | 当前角度: 1.2° | 角度误差: -1.20°
线路误差: 0.8 | 检测传感器: 6个
左电机: 48% | 右电机: 42% | 基础速度: 45%
传感器状态: 陀螺仪=OK | 灰度=OK
控制输出: 角度=-3.0 | 巡线=2.4
========================================
```

---

## 测试验证

### 编译测试
- ✅ 无编译错误
- ✅ 无编译警告
- ✅ 链接成功

### 功能测试计划
1. **硬件连接测试**
   - JY901S数据接收测试
   - 灰度传感器I2C通信测试
   - 电机PWM输出测试
   - OLED显示测试

2. **控制算法测试**
   - 角度控制PID调试
   - 巡线控制权重调试
   - 融合控制模式测试

3. **性能测试**
   - 20Hz控制频率验证
   - 响应时间测量
   - 稳定性长时间测试

---

## 使用说明

### 快速启动
1. 连接硬件 (按README.md中的连接图)
2. 编译并下载程序
3. 观察OLED显示初始化过程
4. 3秒后系统自动启动循迹控制

### 测试模式启用
在main.c中取消注释以下行：
```c
// LineFollowing_System_Test();  // 取消注释启用测试
```

### 参数调整
修改`LineFollowing_System_Init()`中的config结构体参数

---

## 故障排除

### 常见问题
1. **系统初始化失败**
   - 检查硬件连接
   - 确认传感器供电
   - 验证I2C/UART通信

2. **控制效果不佳**
   - 调整PID参数
   - 检查传感器权重配置
   - 验证电机方向

3. **显示异常**
   - 检查OLED连接
   - 确认I2C1通信正常

---

## 版权信息

**版权所有**: 米醋电子工作室  
**开发工程师**: Alex  
**集成日期**: 2025-07-29  
**版本**: V1.0

---

## 更新日志

### V1.0 (2025-07-29)
- ✅ 完成循迹功能完整集成到main.c
- ✅ 实现20Hz实时控制循环
- ✅ 集成OLED状态显示和串口调试
- ✅ 添加完整的错误处理机制
- ✅ 提供功能测试接口
- ✅ 编译测试通过，准备硬件验证

**集成状态**: ✅ **完成** - 准备硬件测试！
