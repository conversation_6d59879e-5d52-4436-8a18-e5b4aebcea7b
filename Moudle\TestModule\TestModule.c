#include "TestModule.h"
#include <stdio.h>
#include <string.h>

/**********************************************************
***	STM32F407测试模块实现
***	编写作者：Alex (工程师)
***	功能：封装所有测试和辅助函数
***	版本：V1.0 - 模块化封装版
***	日期：2025-07-29
**********************************************************/

// 宏定义
#ifndef GET_NTH_BIT
#define GET_NTH_BIT(data, n) (((data) >> ((n) - 1)) & 0x01)
#endif

#define EMMV5_X_MOTOR_ADDR  1
#define EMMV5_Y_MOTOR_ADDR  1

// 全局变量
static TestModule_Config_t g_config;
static TestModule_Status_t g_status = {0};
static bool g_initialized = false;
static bool g_debug_mode = false;

// 灰度传感器相关变量
static uint8_t gray_digital_data = 0;
static uint8_t gray_analog_data[8] = {0};

// EMMV5测试相关变量
static uint32_t emmv5_test_counter = 0;

// 外部UART句柄声明
extern UART_HandleTypeDef huart2;
extern UART_HandleTypeDef huart4;

/**********************************************************
*** 核心API接口实现
**********************************************************/

/**
  * @brief 初始化测试模块
  */
HAL_StatusTypeDef TestModule_Init(TestModule_Config_t* config)
{
    // 设置默认配置
    g_config.grayscale_enabled = false;
    g_config.motor_test_enabled = false;
    g_config.emmv5_test_enabled = false;
    g_config.test_interval_ms = 100;
    
    // 如果用户提供了配置，则覆盖默认值
    if (config != NULL) {
        g_config = *config;
    }
    
    // 初始化状态
    g_status.grayscale_connected = false;
    g_status.emmv5_initialized = false;
    g_status.test_counter = 0;
    g_status.last_test_time = HAL_GetTick();
    
    // 根据配置初始化相应模块
    if (g_config.grayscale_enabled) {
        TestModule_GrayScale_Init();
    }
    
    if (g_config.emmv5_test_enabled) {
        TestModule_EMMV5_Init();
    }
    
    g_initialized = true;
    
    return HAL_OK;
}

/**
  * @brief 测试模块主循环更新
  */
void TestModule_Update(void)
{
    if (!g_initialized) {
        return;
    }
    
    uint32_t current_time = HAL_GetTick();
    
    // 检查是否到了测试间隔时间
    if (current_time - g_status.last_test_time >= g_config.test_interval_ms) {
        TestModule_UpdateCounters();
        
        // 处理灰度传感器测试
        if (g_config.grayscale_enabled) {
            TestModule_ProcessGrayScale();
        }
        
        // 处理EMMV5电机测试
        if (g_config.emmv5_test_enabled) {
            TestModule_ProcessEMMV5Test();
        }
        
        g_status.last_test_time = current_time;
    }
}

/**
  * @brief 启用/禁用特定测试
  */
void TestModule_EnableTest(uint8_t test_type, bool enable)
{
    switch (test_type) {
        case 0: // 灰度传感器
            g_config.grayscale_enabled = enable;
            if (enable && !g_status.grayscale_connected) {
                TestModule_GrayScale_Init();
            }
            break;
        case 1: // 电机测试 (预留)
            g_config.motor_test_enabled = enable;
            break;
        case 2: // EMMV5测试
            g_config.emmv5_test_enabled = enable;
            if (enable && !g_status.emmv5_initialized) {
                TestModule_EMMV5_Init();
            } else if (!enable) {
                TestModule_EMMV5_StopAll();
            }
            break;
    }
}

/**
  * @brief 获取测试状态
  */
TestModule_Status_t* TestModule_GetStatus(void)
{
    return &g_status;
}

/**********************************************************
*** 灰度传感器测试接口实现
**********************************************************/

/**
  * @brief 初始化灰度传感器
  */
HAL_StatusTypeDef TestModule_GrayScale_Init(void)
{
    // 延时等待传感器稳定
    HAL_Delay(100);
    
    // 首先尝试扫描I2C地址
    uint8_t addr_found = 0;
    for (uint8_t addr = 0x10; addr <= 0x7F; addr++) {
        if (TestModule_I2C_ScanAddress(addr) == 0) {
            if (addr == 0x4C) {
                addr_found = 1;
                break;
            }
        }
        HAL_Delay(1);
    }
    
    // 检测传感器连接状态
    if (addr_found && Ping() == 0) {
        g_status.grayscale_connected = true;
    } else {
        g_status.grayscale_connected = false;
        
        // 尝试多次检测
        for (int i = 0; i < 3; i++) {
            HAL_Delay(50);
            if (Ping() == 0) {
                g_status.grayscale_connected = true;
                break;
            }
        }
    }
    
    return g_status.grayscale_connected ? HAL_OK : HAL_ERROR;
}

/**
  * @brief 读取灰度传感器数据
  */
void TestModule_GrayScale_ReadData(void)
{
    if (g_status.grayscale_connected) {
        // 读取数字量数据
        gray_digital_data = IIC_Get_Digtal();
        
        // 读取模拟量数据
        IIC_Get_Anolog(gray_analog_data, 8);
    }
}

/**
  * @brief 在OLED上显示灰度传感器数据
  */
void TestModule_GrayScale_ShowData(uint8_t line)
{
    if (g_status.grayscale_connected) {
        // 显示数字量数据 (8位二进制)
        OLED_ShowStr(0, line, "G:", 8);
        
        // 显示前4位数字量
        for (int i = 0; i < 4; i++) {
            uint8_t bit = GET_NTH_BIT(gray_digital_data, i + 1);
            OLED_ShowNum(16 + i * 8, line, bit, 1, 8);
        }
        
        // 显示后4位数字量
        for (int i = 0; i < 4; i++) {
            uint8_t bit = GET_NTH_BIT(gray_digital_data, i + 5);
            OLED_ShowNum(56 + i * 8, line, bit, 1, 8);
        }
        
        // 显示模拟量平均值
        uint16_t avg = 0;
        for (int i = 0; i < 8; i++) {
            avg += gray_analog_data[i];
        }
        avg /= 8;
        OLED_ShowStr(96, line, "A:", 8);
        OLED_ShowNum(112, line, avg, 3, 8);
    } else {
        OLED_ShowStr(0, line, "Gray: Disconnected", 8);
    }
}

/**
  * @brief 获取灰度传感器连接状态
  */
bool TestModule_GrayScale_IsConnected(void)
{
    return g_status.grayscale_connected;
}

/**********************************************************
*** EMMV5电机测试接口实现
**********************************************************/

/**
  * @brief 初始化EMMV5双轴电机
  */
HAL_StatusTypeDef TestModule_EMMV5_Init(void)
{
    // 延时等待电机稳定
    HAL_Delay(100);
    
    // 初始化X轴电机 (USART2)
    Emm_V5_Modify_Ctrl_Mode(&huart2, EMMV5_X_MOTOR_ADDR, false, 2);
    HAL_Delay(10);
    Emm_V5_En_Control(&huart2, EMMV5_X_MOTOR_ADDR, true, false);
    HAL_Delay(10);
    
    // 初始化Y轴电机 (UART4)
    Emm_V5_Modify_Ctrl_Mode(&huart4, EMMV5_Y_MOTOR_ADDR, false, 2);
    HAL_Delay(10);
    Emm_V5_En_Control(&huart4, EMMV5_Y_MOTOR_ADDR, true, false);
    HAL_Delay(10);
    
    g_status.emmv5_initialized = true;
    
    return HAL_OK;
}

/**
  * @brief EMMV5电机测试更新
  */
void TestModule_EMMV5_TestUpdate(void)
{
    if (!g_status.emmv5_initialized) {
        return;
    }
    
    emmv5_test_counter++;
    
    // 每2秒改变一次电机状态 (100ms * 20 = 2s)
    if (emmv5_test_counter % 20 == 0) {
        uint8_t cycle = (emmv5_test_counter / 20) % 10;  // 10个周期循环
        
        // 前5个周期测试X轴，后5个周期测试Y轴
        if (cycle < 5) {
            // X轴测试序列: 停止 -> 正转 -> 停止 -> 反转 -> 停止
            switch (cycle) {
                case 0:
                    // 停止X轴，确保Y轴也停止
                    Emm_V5_Stop_Now(&huart2, EMMV5_X_MOTOR_ADDR, false);
                    Emm_V5_Stop_Now(&huart4, EMMV5_Y_MOTOR_ADDR, false);
                    break;
                case 1:
                    // X轴正转 100 RPM
                    Emm_V5_Vel_Control(&huart2, EMMV5_X_MOTOR_ADDR, 0, 100, 0, false);
                    break;
                case 2:
                    // 停止X轴
                    Emm_V5_Stop_Now(&huart2, EMMV5_X_MOTOR_ADDR, false);
                    break;
                case 3:
                    // X轴反转 100 RPM
                    Emm_V5_Vel_Control(&huart2, EMMV5_X_MOTOR_ADDR, 1, 100, 0, false);
                    break;
                case 4:
                    // 停止X轴
                    Emm_V5_Stop_Now(&huart2, EMMV5_X_MOTOR_ADDR, false);
                    break;
            }
        } else {
            // Y轴测试序列: 停止 -> 正转 -> 停止 -> 反转 -> 停止
            switch (cycle - 5) {
                case 0:
                    // 停止Y轴，确保X轴也停止
                    Emm_V5_Stop_Now(&huart2, EMMV5_X_MOTOR_ADDR, false);
                    Emm_V5_Stop_Now(&huart4, EMMV5_Y_MOTOR_ADDR, false);
                    break;
                case 1:
                    // Y轴正转 100 RPM
                    Emm_V5_Vel_Control(&huart4, EMMV5_Y_MOTOR_ADDR, 0, 100, 10, false);
                    break;
                case 2:
                    // 停止Y轴
                    Emm_V5_Stop_Now(&huart4, EMMV5_Y_MOTOR_ADDR, false);
                    break;
                case 3:
                    // Y轴反转 100 RPM
                    Emm_V5_Vel_Control(&huart4, EMMV5_Y_MOTOR_ADDR, 1, 100, 10, false);
                    break;
                case 4:
                    // 停止Y轴
                    Emm_V5_Stop_Now(&huart4, EMMV5_Y_MOTOR_ADDR, false);
                    break;
            }
        }
    }
}

/**
  * @brief 在OLED上显示EMMV5电机状态
  */
void TestModule_EMMV5_ShowStatus(uint8_t x, uint8_t y)
{
    if (!g_status.emmv5_initialized) {
        OLED_ShowStr(x, y, "EMMV5: Not Init", 8);
        return;
    }

    // 显示X轴状态
    OLED_ShowStr(x, y, "X:", 8);
    uint8_t x_status = (emmv5_test_counter / 20) % 10;  // X轴周期
    if (x_status < 5) {
        switch(x_status) {
            case 0: case 2: case 4:
                OLED_ShowStr(x + 16, y, "S", 8);  // X轴停止
                break;
            case 1:
                OLED_ShowStr(x + 16, y, "+", 8);  // X轴正转
                break;
            case 3:
                OLED_ShowStr(x + 16, y, "-", 8);  // X轴反转
                break;
        }
    } else {
        OLED_ShowStr(x + 16, y, "S", 8);  // X轴停止
    }

    // 显示Y轴状态
    OLED_ShowStr(x + 32, y, "Y:", 8);
    uint8_t y_status = (emmv5_test_counter / 20) % 10;  // Y轴周期
    if (y_status >= 5) {
        switch((y_status - 5)) {
            case 0: case 2: case 4:
                OLED_ShowStr(x + 48, y, "S", 8);  // Y轴停止
                break;
            case 1:
                OLED_ShowStr(x + 48, y, "+", 8);  // Y轴正转
                break;
            case 3:
                OLED_ShowStr(x + 48, y, "-", 8);  // Y轴反转
                break;
        }
    } else {
        OLED_ShowStr(x + 48, y, "S", 8);  // Y轴停止
    }
}

/**
  * @brief 停止所有EMMV5电机
  */
void TestModule_EMMV5_StopAll(void)
{
    if (g_status.emmv5_initialized) {
        Emm_V5_Stop_Now(&huart2, EMMV5_X_MOTOR_ADDR, false);
        Emm_V5_Stop_Now(&huart4, EMMV5_Y_MOTOR_ADDR, false);
    }
}

/**********************************************************
*** 辅助工具接口实现
**********************************************************/

/**
  * @brief 在OLED上显示带符号的角度
  */
void TestModule_OLED_ShowSignedAngle(uint8_t x, uint8_t y, int16_t angle)
{
    if (angle < 0) {
        OLED_ShowStr(x, y, "-", 8);
        OLED_ShowNum(x + 8, y, -angle, 3, 8);
    } else {
        OLED_ShowStr(x, y, "+", 8);
        OLED_ShowNum(x + 8, y, angle, 3, 8);
    }
}

/**
  * @brief I2C地址扫描
  */
uint8_t TestModule_I2C_ScanAddress(uint8_t addr)
{
    IIC_Start();
    uint8_t ack = IIC_SendByte(addr << 1);  // 发送地址+写位
    IIC_Stop();
    return ack;  // 返回应答状态
}

/**
  * @brief 扫描所有I2C设备
  */
uint8_t TestModule_I2C_ScanAllDevices(uint8_t* found_devices, uint8_t max_devices)
{
    uint8_t device_count = 0;

    for (uint8_t addr = 0x08; addr <= 0x77 && device_count < max_devices; addr++) {
        if (TestModule_I2C_ScanAddress(addr) == 0) {
            found_devices[device_count] = addr;
            device_count++;
        }
        HAL_Delay(1);  // 小延时避免总线拥塞
    }

    return device_count;
}

/**********************************************************
*** 调试接口实现
**********************************************************/

/**
  * @brief 启用/禁用调试模式
  */
void TestModule_SetDebugMode(bool enable)
{
    g_debug_mode = enable;
}

/**
  * @brief 重置测试计数器
  */
void TestModule_ResetCounters(void)
{
    g_status.test_counter = 0;
    emmv5_test_counter = 0;
}

/**
  * @brief 获取测试统计信息字符串
  */
void TestModule_GetStatsString(char* buffer, uint16_t size)
{
    if (buffer != NULL && size > 0) {
        snprintf(buffer, size, "Tests:%lu EMMV5:%s Gray:%s",
                g_status.test_counter,
                g_status.emmv5_initialized ? "OK" : "NO",
                g_status.grayscale_connected ? "OK" : "NO");
    }
}

/**********************************************************
*** 内部函数实现
**********************************************************/

/**
  * @brief 处理灰度传感器测试
  */
void TestModule_ProcessGrayScale(void)
{
    TestModule_GrayScale_ReadData();
}

/**
  * @brief 处理EMMV5电机测试
  */
void TestModule_ProcessEMMV5Test(void)
{
    TestModule_EMMV5_TestUpdate();
}

/**
  * @brief 更新计数器
  */
void TestModule_UpdateCounters(void)
{
    g_status.test_counter++;
}
