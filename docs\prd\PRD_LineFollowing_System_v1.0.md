# 循迹功能产品需求文档 (PRD)
**Product Requirements Document - Line Following System**

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| **文档标题** | STM32F407循迹功能产品需求文档 |
| **版本号** | V1.0 |
| **编写日期** | 2025-07-29 |
| **负责人** | Emma (产品经理) |
| **项目代号** | LineFollowing_System |
| **文档状态** | 初版完成 |

### 1.1 版本历史
| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| V1.0 | 2025-07-29 | 初版创建，完整功能规格定义 | Emma |

---

## 2. 背景与问题陈述

### 2.1 项目背景
基于STM32F407开发板的智能小车系统，需要实现精确的循迹功能。当前系统已具备：
- **JY901S姿态传感器**：提供三轴角度数据，特别是Yaw角度用于方向控制
- **8路灰度传感器**：通过I2C接口提供数字量和模拟量检测
- **DRV8871电机驱动**：双路直流电机PWM控制
- **完整的硬件驱动模块**：已验证的传感器和电机驱动代码

### 2.2 核心问题
1. **直线行驶精度不足**：缺乏基于陀螺仪的角度闭环控制
2. **巡线算法缺失**：8路灰度传感器数据未有效利用
3. **转弯控制不精确**：无法通过设定目标角度实现精确转弯
4. **传感器数据融合缺失**：陀螺仪和灰度传感器数据未协同工作

### 2.3 解决方案价值
- **提升行驶精度**：通过陀螺仪角度控制确保直线行驶
- **实现智能巡线**：8路灰度传感器权重算法保证线路跟踪
- **支持精确转弯**：可编程角度控制实现预设路径
- **增强系统稳定性**：多传感器融合提高控制可靠性

---

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **O1 - 直线行驶精度**：实现基于JY901S Yaw角的直线行驶控制
2. **O2 - 智能巡线功能**：实现基于8路灰度传感器的线路跟踪
3. **O3 - 精确转弯控制**：支持设定目标角度的转弯功能
4. **O4 - 系统集成**：将循迹功能集成到现有系统架构

### 3.2 关键结果 (Key Results)
| 指标 | 目标值 | 测量方法 |
|------|--------|----------|
| **直线偏差角度** | ±2° | JY901S Yaw角度测量 |
| **巡线响应时间** | <50ms | 灰度传感器数据处理延迟 |
| **转弯精度** | ±5° | 目标角度与实际角度差值 |
| **系统稳定性** | >95% | 连续运行成功率 |
| **控制频率** | ≥20Hz | 主控制循环频率 |

### 3.3 反向指标 (Counter Metrics)
- **功耗增加** < 10%（相比现有系统）
- **代码复杂度** < 2000行（核心循迹模块）
- **内存占用** < 5KB（循迹功能相关）

---

## 4. 用户画像与用户故事

### 4.1 目标用户
- **嵌入式开发工程师**：需要可靠的循迹功能模块
- **机器人竞赛参与者**：要求高精度的路径跟踪
- **教育用户**：学习传感器融合和控制算法

### 4.2 用户故事

#### 4.2.1 直线行驶场景
**作为** 系统操作者  
**我希望** 小车能够在设定目标角度为0°时保持直线行驶  
**以便于** 确保小车不会因为机械误差或环境干扰偏离直线路径

**验收标准：**
- 设定目标角度0°后，小车Yaw角度保持在±2°范围内
- 在10米直线距离内，横向偏移<20cm
- 系统能自动纠正由外力造成的偏差

#### 4.2.2 精确转弯场景
**作为** 系统操作者  
**我希望** 能够通过设定特定角度（如90°、180°）来实现精确转弯  
**以便于** 小车能够按照预定路径行驶，完成复杂的导航任务

**验收标准：**
- 支持设定任意目标角度（-180°到+180°）
- 转弯完成后角度误差<±5°
- 转弯过程平滑，无明显震荡

#### 4.2.3 智能巡线场景
**作为** 系统操作者  
**我希望** 小车能够利用8路灰度传感器自动跟踪黑线  
**以便于** 小车能够沿着预设的线路轨迹行驶，适应不同的线路形状

**验收标准：**
- 8个传感器从内到外具有不同的控制权重
- 能够检测和纠正偏离线路的情况
- 支持直线、弯道、交叉路口等复杂线路

---

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 角度控制模块 (AngleControl)
**功能描述：** 基于JY901S陀螺仪实现精确的角度控制

**输入参数：**
- `target_angle`: 目标角度 (-180° ~ +180°)
- `current_yaw`: 当前Yaw角度 (来自JY901S)
- `control_mode`: 控制模式 (HOLD_ANGLE/TURN_TO_ANGLE)

**输出参数：**
- `angle_error`: 角度误差
- `control_output`: 控制输出 (-100 ~ +100)

**控制算法：**
```c
// PID控制器参数
typedef struct {
    float kp;           // 比例系数 (建议值: 2.0)
    float ki;           // 积分系数 (建议值: 0.1)
    float kd;           // 微分系数 (建议值: 0.5)
    float integral;     // 积分累积
    float last_error;   // 上次误差
} AnglePID_t;
```

#### 5.1.2 巡线控制模块 (LineFollowing)
**功能描述：** 基于8路灰度传感器实现智能巡线

**传感器布局：**
```
[0] [1] [2] [3] [4] [5] [6] [7]
 外  内  内  内  内  内  内  外
```

**权重配置：**
| 传感器位置 | 权重系数 | 控制作用 |
|------------|----------|----------|
| 0 (最外侧) | -4 | 强左转 |
| 1 | -2 | 中左转 |
| 2 | -1 | 微左转 |
| 3 | -0.5 | 微左转 |
| 4 | +0.5 | 微右转 |
| 5 | +1 | 微右转 |
| 6 | +2 | 中右转 |
| 7 (最外侧) | +4 | 强右转 |

**巡线算法：**
```c
// 计算线路偏差
float line_error = 0;
for(int i = 0; i < 8; i++) {
    if(gray_digital_data & (1 << i)) {
        line_error += weight[i];
    }
}
```

#### 5.1.3 电机控制模块 (MotorControl)
**功能描述：** 融合角度控制和巡线控制，输出电机控制信号

**控制融合策略：**
```c
// 控制输出融合
left_motor_speed = base_speed + angle_control_output + line_control_output;
right_motor_speed = base_speed - angle_control_output - line_control_output;
```

**速度限制：**
- 基础速度：30-70% (可配置)
- 最大修正幅度：±50%
- 最终输出范围：-100% ~ +100%

### 5.2 工作模式定义

#### 5.2.1 纯角度控制模式 (ANGLE_ONLY)
- 仅使用JY901S角度数据
- 适用于直线行驶和精确转弯
- 忽略灰度传感器数据

#### 5.2.2 纯巡线模式 (LINE_ONLY)
- 仅使用灰度传感器数据
- 适用于标准黑线跟踪
- 忽略角度控制

#### 5.2.3 融合控制模式 (FUSION)
- 同时使用角度和巡线控制
- 角度控制保证大方向正确
- 巡线控制保证精确跟线
- **默认推荐模式**

### 5.3 API接口设计

#### 5.3.1 初始化接口
```c
// 循迹系统初始化
HAL_StatusTypeDef LineFollowing_Init(LineFollowing_Config_t* config);

// 配置结构体
typedef struct {
    float angle_kp, angle_ki, angle_kd;    // 角度PID参数
    float line_kp, line_ki, line_kd;       // 巡线PID参数
    uint8_t base_speed;                    // 基础速度 (0-100)
    LineFollowing_Mode_t mode;             // 工作模式
    float weight[8];                       // 灰度传感器权重
} LineFollowing_Config_t;
```

#### 5.3.2 控制接口
```c
// 设定目标角度
void LineFollowing_SetTargetAngle(float target_angle);

// 设定工作模式
void LineFollowing_SetMode(LineFollowing_Mode_t mode);

// 设定基础速度
void LineFollowing_SetBaseSpeed(uint8_t speed);

// 主控制循环 (在main loop中调用)
void LineFollowing_Update(void);

// 启动/停止循迹
void LineFollowing_Start(void);
void LineFollowing_Stop(void);
```

#### 5.3.3 状态查询接口
```c
// 获取当前状态
LineFollowing_Status_t LineFollowing_GetStatus(void);

// 获取控制数据
void LineFollowing_GetControlData(float* angle_error, float* line_error, 
                                  int8_t* left_speed, int8_t* right_speed);
```

---

## 6. 范围定义

### 6.1 包含功能 (In Scope)

#### 6.1.1 核心功能
- ✅ JY901S角度数据读取和处理
- ✅ 8路灰度传感器数据读取和处理  
- ✅ PID角度控制算法实现
- ✅ 权重化巡线算法实现
- ✅ 双电机差速控制
- ✅ 多模式控制切换
- ✅ 实时状态监控和调试

#### 6.1.2 集成功能
- ✅ 与现有OLED显示系统集成
- ✅ 与现有电机驱动系统集成
- ✅ 与现有传感器系统集成
- ✅ 模块化代码设计，便于维护

#### 6.1.3 配置功能
- ✅ PID参数在线调节
- ✅ 传感器权重配置
- ✅ 速度参数配置
- ✅ 工作模式切换

### 6.2 排除功能 (Out of Scope)

#### 6.2.1 高级功能
- ❌ 机器视觉循迹 (K230D相关功能保持独立)
- ❌ 路径规划和导航
- ❌ 障碍物检测和避障
- ❌ 多车协同控制

#### 6.2.2 硬件功能
- ❌ 新增传感器硬件支持
- ❌ 步进电机控制 (EMMV5保持独立)
- ❌ 无线通信功能
- ❌ 电源管理功能

#### 6.2.3 系统功能
- ❌ 实时操作系统集成
- ❌ 文件系统支持
- ❌ 网络功能
- ❌ 图形用户界面

---

## 7. 依赖与风险

### 7.1 内部依赖项
| 依赖模块 | 依赖内容 | 风险等级 | 缓解措施 |
|----------|----------|----------|----------|
| **JY901S模块** | 角度数据读取 | 低 | 已验证稳定 |
| **灰度传感器模块** | I2C数据读取 | 中 | 增加错误处理 |
| **电机驱动模块** | PWM控制接口 | 低 | 已验证稳定 |
| **OLED显示模块** | 状态显示 | 低 | 可选依赖 |

### 7.2 外部依赖项
| 依赖项 | 描述 | 风险等级 | 缓解措施 |
|--------|------|----------|----------|
| **STM32 HAL库** | 硬件抽象层 | 低 | 标准库，稳定 |
| **硬件平台** | STM32F407开发板 | 低 | 已验证平台 |
| **传感器硬件** | 物理传感器正常工作 | 中 | 增加自检功能 |

### 7.3 潜在风险

#### 7.3.1 技术风险
| 风险 | 概率 | 影响 | 缓解策略 |
|------|------|------|----------|
| **传感器数据噪声** | 中 | 中 | 数字滤波算法 |
| **PID参数调节困难** | 中 | 中 | 提供调试工具 |
| **电机响应延迟** | 低 | 中 | 增加前馈控制 |
| **系统资源不足** | 低 | 高 | 代码优化 |

#### 7.3.2 集成风险
| 风险 | 概率 | 影响 | 缓解策略 |
|------|------|------|----------|
| **与现有代码冲突** | 中 | 高 | 模块化设计 |
| **实时性要求冲突** | 中 | 中 | 优化调度策略 |
| **硬件资源竞争** | 低 | 中 | 资源管理机制 |

---

## 8. 发布初步计划

### 8.1 开发阶段规划

#### 8.1.1 阶段一：基础功能开发 (预计2天)
- **目标**：实现核心循迹算法
- **交付物**：
  - 角度控制模块代码
  - 巡线控制模块代码
  - 基础电机控制接口
- **验收标准**：单元测试通过，基础功能可用

#### 8.1.2 阶段二：系统集成 (预计1天)
- **目标**：集成到现有系统
- **交付物**：
  - 完整的循迹功能模块
  - OLED显示集成
  - 配置接口实现
- **验收标准**：集成测试通过，无系统冲突

#### 8.1.3 阶段三：测试优化 (预计1天)
- **目标**：功能验证和性能优化
- **交付物**：
  - 完整测试报告
  - 性能优化代码
  - 用户使用文档
- **验收标准**：所有功能指标达标

### 8.2 测试策略

#### 8.2.1 单元测试
- 角度控制算法测试
- 巡线算法测试
- 电机控制接口测试

#### 8.2.2 集成测试
- 传感器数据融合测试
- 多模式切换测试
- 系统稳定性测试

#### 8.2.3 功能测试
- 直线行驶精度测试
- 转弯精度测试
- 巡线跟踪测试

### 8.3 数据跟踪计划
- **性能指标监控**：角度误差、响应时间、控制精度
- **系统资源监控**：CPU使用率、内存占用、实时性
- **用户反馈收集**：功能易用性、稳定性、扩展性

---

## 9. 附录

### 9.1 技术参考
- STM32F407参考手册
- JY901S数据手册
- DRV8871电机驱动芯片手册
- 灰度传感器技术规格

### 9.2 相关文档
- 《STM32F407_Multi_Sensor_System_User_Guide.md》
- 现有硬件驱动模块代码
- 系统架构设计文档

---

**文档结束**

> **备注**：本PRD文档为V1.0版本，后续将根据开发进展和测试反馈进行迭代更新。所有技术实现细节将在架构设计文档中进一步详述。
