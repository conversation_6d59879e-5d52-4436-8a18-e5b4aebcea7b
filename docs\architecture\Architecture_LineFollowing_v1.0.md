# 循迹系统架构设计文档
**System Architecture Document - Line Following System**

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| **文档标题** | 循迹系统软件架构设计 |
| **版本号** | V1.0 |
| **编写日期** | 2025-07-29 |
| **负责人** | Bob (架构师) |
| **项目代号** | LineFollowing_Architecture |

---

## 2. 系统架构概览

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    循迹控制系统                              │
├─────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                 │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  主控制循环      │  │  配置管理        │                  │
│  │  LineFollowing  │  │  Config Manager │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  控制层 (Control Layer)                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 角度控制     │ │ 巡线控制     │ │ 电机控制     │           │
│  │ AngleCtrl   │ │ LineCtrl    │ │ MotorCtrl   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 传感器融合   │ │ 滤波处理     │ │ 状态管理     │           │
│  │ SensorFusion│ │ Filter      │ │ StateManager│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  硬件抽象层 (HAL Layer)                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ JY901S接口   │ │ 灰度传感器   │ │ 电机驱动     │           │
│  │ JY901S_HAL  │ │ Gray_HAL    │ │ Motor_HAL   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心模块职责

| 模块 | 职责 | 输入 | 输出 |
|------|------|------|------|
| **AngleControl** | 角度PID控制 | 目标角度、当前角度 | 角度控制输出 |
| **LineControl** | 巡线权重控制 | 8路灰度数据 | 巡线控制输出 |
| **MotorControl** | 电机差速控制 | 控制信号 | 左右电机PWM |
| **SensorFusion** | 传感器数据融合 | 原始传感器数据 | 融合后数据 |
| **StateManager** | 系统状态管理 | 各模块状态 | 系统运行状态 |

---

## 3. 详细模块设计

### 3.1 角度控制模块 (AngleControl)

#### 3.1.1 模块结构
```c
typedef struct {
    // PID控制器参数
    float kp, ki, kd;           // PID系数
    float integral;             // 积分累积
    float last_error;           // 上次误差
    float output_limit;         // 输出限制
    
    // 控制状态
    float target_angle;         // 目标角度
    float current_angle;        // 当前角度
    float angle_error;          // 角度误差
    float control_output;       // 控制输出
    
    // 配置参数
    uint32_t update_period;     // 更新周期(ms)
    uint32_t last_update;       // 上次更新时间
    bool enabled;               // 使能标志
} AngleControl_t;
```

#### 3.1.2 核心算法
```c
// PID控制算法实现
float AngleControl_PID(AngleControl_t* ctrl, float target, float current) {
    // 计算误差 (处理角度环绕)
    float error = target - current;
    if (error > 180.0f) error -= 360.0f;
    if (error < -180.0f) error += 360.0f;
    
    // PID计算
    ctrl->integral += error;
    float derivative = error - ctrl->last_error;
    
    float output = ctrl->kp * error + 
                   ctrl->ki * ctrl->integral + 
                   ctrl->kd * derivative;
    
    // 输出限制
    if (output > ctrl->output_limit) output = ctrl->output_limit;
    if (output < -ctrl->output_limit) output = -ctrl->output_limit;
    
    ctrl->last_error = error;
    return output;
}
```

### 3.2 巡线控制模块 (LineControl)

#### 3.2.1 模块结构
```c
typedef struct {
    // 传感器权重配置
    float weights[8];           // 8路传感器权重
    uint8_t sensor_threshold;   // 数字化阈值
    
    // 控制参数
    float kp, ki, kd;           // PID系数
    float integral;             // 积分累积
    float last_error;           // 上次误差
    
    // 状态数据
    uint8_t digital_data;       // 数字量数据
    uint8_t analog_data[8];     // 模拟量数据
    float line_error;           // 线路误差
    float control_output;       // 控制输出
    
    // 配置参数
    bool enabled;               // 使能标志
    uint32_t last_update;       // 上次更新时间
} LineControl_t;
```

#### 3.2.2 权重算法
```c
// 线路误差计算
float LineControl_CalculateError(LineControl_t* ctrl) {
    float weighted_sum = 0;
    float total_active = 0;
    
    // 权重化计算
    for (int i = 0; i < 8; i++) {
        if (ctrl->digital_data & (1 << i)) {
            weighted_sum += ctrl->weights[i];
            total_active += 1.0f;
        }
    }
    
    // 归一化处理
    if (total_active > 0) {
        return weighted_sum / total_active;
    }
    return 0; // 无线检测
}
```

### 3.3 电机控制模块 (MotorControl)

#### 3.3.1 模块结构
```c
typedef struct {
    // 电机参数
    Motor_t* left_motor;        // 左电机句柄
    Motor_t* right_motor;       // 右电机句柄
    
    // 控制参数
    uint8_t base_speed;         // 基础速度 (0-100)
    float max_correction;       // 最大修正幅度
    
    // 控制输入
    float angle_control;        // 角度控制输入
    float line_control;         // 巡线控制输入
    
    // 输出状态
    int8_t left_speed;          // 左电机速度
    int8_t right_speed;         // 右电机速度
    
    // 配置参数
    bool enabled;               // 使能标志
    MotorControl_Mode_t mode;   // 控制模式
} MotorControl_t;
```

#### 3.3.2 差速控制算法
```c
// 差速控制实现
void MotorControl_Update(MotorControl_t* ctrl) {
    if (!ctrl->enabled) {
        Motor_SetSpeed(ctrl->left_motor, 0);
        Motor_SetSpeed(ctrl->right_motor, 0);
        return;
    }
    
    // 控制信号融合
    float total_correction = ctrl->angle_control + ctrl->line_control;
    
    // 限制修正幅度
    if (total_correction > ctrl->max_correction) 
        total_correction = ctrl->max_correction;
    if (total_correction < -ctrl->max_correction) 
        total_correction = -ctrl->max_correction;
    
    // 差速计算
    float left_speed = ctrl->base_speed + total_correction;
    float right_speed = ctrl->base_speed - total_correction;
    
    // 速度限制
    left_speed = CLAMP(left_speed, -100, 100);
    right_speed = CLAMP(right_speed, -100, 100);
    
    // 输出到电机
    Motor_SetSpeed(ctrl->left_motor, left_speed);
    Motor_SetSpeed(ctrl->right_motor, right_speed);
    
    // 保存状态
    ctrl->left_speed = (int8_t)left_speed;
    ctrl->right_speed = (int8_t)right_speed;
}
```

---

## 4. 数据流设计

### 4.1 数据流图
```
JY901S传感器 ──→ [角度数据] ──→ AngleControl ──┐
                                              │
8路灰度传感器 ──→ [线路数据] ──→ LineControl ───┤
                                              │
                                              ▼
                                        MotorControl
                                              │
                                              ▼
                                      [左右电机PWM]
```

### 4.2 数据结构定义

#### 4.2.1 传感器数据结构
```c
// 传感器原始数据
typedef struct {
    // JY901S数据
    float yaw_angle;            // Yaw角度
    float roll_angle;           // Roll角度  
    float pitch_angle;          // Pitch角度
    uint32_t angle_timestamp;   // 角度数据时间戳
    
    // 灰度传感器数据
    uint8_t gray_digital;       // 8位数字量
    uint8_t gray_analog[8];     // 8路模拟量
    uint32_t gray_timestamp;    // 灰度数据时间戳
    
    // 数据有效性
    bool angle_valid;           // 角度数据有效
    bool gray_valid;            // 灰度数据有效
} SensorData_t;
```

#### 4.2.2 控制数据结构
```c
// 控制系统数据
typedef struct {
    // 控制目标
    float target_angle;         // 目标角度
    LineFollowing_Mode_t mode;  // 控制模式
    uint8_t base_speed;         // 基础速度
    
    // 控制状态
    float angle_error;          // 角度误差
    float line_error;           // 线路误差
    float angle_output;         // 角度控制输出
    float line_output;          // 巡线控制输出
    
    // 电机状态
    int8_t left_motor_speed;    // 左电机速度
    int8_t right_motor_speed;   // 右电机速度
    
    // 系统状态
    LineFollowing_State_t state; // 系统状态
    uint32_t update_count;      // 更新计数
    uint32_t last_update;       // 最后更新时间
} ControlData_t;
```

---

## 5. 控制流程设计

### 5.1 主控制流程
```c
void LineFollowing_Update(void) {
    // 1. 数据采集
    SensorData_t sensor_data;
    if (SensorFusion_GetData(&sensor_data) != HAL_OK) {
        return; // 数据无效，跳过本次更新
    }
    
    // 2. 角度控制
    float angle_output = 0;
    if (g_config.mode != LINE_ONLY) {
        angle_output = AngleControl_Update(&g_angle_ctrl, 
                                          g_config.target_angle, 
                                          sensor_data.yaw_angle);
    }
    
    // 3. 巡线控制  
    float line_output = 0;
    if (g_config.mode != ANGLE_ONLY) {
        line_output = LineControl_Update(&g_line_ctrl, 
                                        sensor_data.gray_digital,
                                        sensor_data.gray_analog);
    }
    
    // 4. 电机控制
    MotorControl_SetInputs(&g_motor_ctrl, angle_output, line_output);
    MotorControl_Update(&g_motor_ctrl);
    
    // 5. 状态更新
    StateManager_Update(&g_state_mgr);
}
```

### 5.2 模式切换流程
```c
typedef enum {
    ANGLE_ONLY = 0,     // 纯角度控制
    LINE_ONLY,          // 纯巡线控制  
    FUSION              // 融合控制
} LineFollowing_Mode_t;

void LineFollowing_SetMode(LineFollowing_Mode_t mode) {
    // 模式切换时重置控制器状态
    AngleControl_Reset(&g_angle_ctrl);
    LineControl_Reset(&g_line_ctrl);
    
    g_config.mode = mode;
    
    // 根据模式配置控制器使能
    g_angle_ctrl.enabled = (mode != LINE_ONLY);
    g_line_ctrl.enabled = (mode != ANGLE_ONLY);
}
```

---

## 6. 系统集成方案

### 6.1 与现有模块集成

#### 6.1.1 JY901S模块集成
```c
// 利用现有JY901S接口
extern JY901S_Handle jy901s_handle;
extern JY901S_Data sensor_data;

// 角度数据获取接口
HAL_StatusTypeDef LineFollowing_GetAngleData(float* yaw_angle) {
    if (JY901S_GetData(&jy901s_handle, &sensor_data) == HAL_OK) {
        *yaw_angle = sensor_data.angle[2]; // Yaw角度
        return HAL_OK;
    }
    return HAL_ERROR;
}
```

#### 6.1.2 灰度传感器模块集成
```c
// 利用现有灰度传感器接口
extern uint8_t gray_digital_data;
extern uint8_t gray_analog_data[8];

// 灰度数据获取接口
HAL_StatusTypeDef LineFollowing_GetGrayData(uint8_t* digital, uint8_t* analog) {
    if (GrayScale_ReadData() == HAL_OK) {
        *digital = gray_digital_data;
        memcpy(analog, gray_analog_data, 8);
        return HAL_OK;
    }
    return HAL_ERROR;
}
```

#### 6.1.3 电机驱动模块集成
```c
// 利用现有电机驱动接口
extern Motor_Handle motor_left;
extern Motor_Handle motor_right;

// 电机控制接口封装
void LineFollowing_SetMotorSpeeds(float left_speed, float right_speed) {
    Motor_SetSpeed(&motor_left, left_speed);
    Motor_SetSpeed(&motor_right, right_speed);
}
```

### 6.2 配置管理集成
```c
// 配置参数结构
typedef struct {
    // 角度控制参数
    float angle_kp, angle_ki, angle_kd;
    
    // 巡线控制参数  
    float line_kp, line_ki, line_kd;
    float line_weights[8];
    
    // 电机控制参数
    uint8_t base_speed;
    float max_correction;
    
    // 系统参数
    LineFollowing_Mode_t mode;
    uint32_t update_period;
} LineFollowing_Config_t;

// 默认配置
static const LineFollowing_Config_t default_config = {
    // 角度PID参数 (需要实际调试)
    .angle_kp = 2.0f,
    .angle_ki = 0.1f, 
    .angle_kd = 0.5f,
    
    // 巡线PID参数
    .line_kp = 1.5f,
    .line_ki = 0.05f,
    .line_kd = 0.3f,
    
    // 传感器权重 (从左到右)
    .line_weights = {-4.0f, -2.0f, -1.0f, -0.5f, 0.5f, 1.0f, 2.0f, 4.0f},
    
    // 电机参数
    .base_speed = 50,       // 50%基础速度
    .max_correction = 30,   // 最大30%修正
    
    // 系统参数
    .mode = FUSION,
    .update_period = 50     // 50ms更新周期 (20Hz)
};
```

---

## 7. 性能优化策略

### 7.1 计算优化
- **浮点运算优化**: 关键路径使用定点运算
- **查表法**: 三角函数使用查表替代
- **内联函数**: 频繁调用的小函数使用inline

### 7.2 内存优化
- **静态分配**: 避免动态内存分配
- **数据对齐**: 结构体成员按4字节对齐
- **缓存友好**: 相关数据放在连续内存

### 7.3 实时性优化
- **中断优先级**: 传感器中断设置高优先级
- **任务调度**: 控制循环使用定时器触发
- **代码分段**: 将关键代码放在SRAM中执行

---

## 8. 错误处理与容错设计

### 8.1 传感器故障处理
```c
typedef enum {
    SENSOR_OK = 0,
    SENSOR_TIMEOUT,
    SENSOR_DATA_INVALID,
    SENSOR_HARDWARE_ERROR
} SensorError_t;

// 传感器故障检测
SensorError_t SensorFusion_CheckHealth(void) {
    // 检查数据更新时间
    uint32_t current_time = HAL_GetTick();
    if (current_time - g_sensor_data.angle_timestamp > 200) {
        return SENSOR_TIMEOUT;
    }
    
    // 检查数据合理性
    if (g_sensor_data.yaw_angle < -180 || g_sensor_data.yaw_angle > 180) {
        return SENSOR_DATA_INVALID;
    }
    
    return SENSOR_OK;
}
```

### 8.2 控制系统保护
```c
// 控制输出限制
void ControlSystem_SafetyCheck(ControlData_t* ctrl_data) {
    // 角度误差过大保护
    if (fabs(ctrl_data->angle_error) > 45.0f) {
        // 降低基础速度
        ctrl_data->base_speed = ctrl_data->base_speed / 2;
    }
    
    // 电机速度限制
    ctrl_data->left_motor_speed = CLAMP(ctrl_data->left_motor_speed, -80, 80);
    ctrl_data->right_motor_speed = CLAMP(ctrl_data->right_motor_speed, -80, 80);
}
```

---

## 9. 调试与监控接口

### 9.1 调试数据结构
```c
typedef struct {
    // 实时数据
    float current_yaw;
    float target_angle;
    float angle_error;
    float line_error;
    
    // 控制输出
    float angle_control_out;
    float line_control_out;
    int8_t left_motor_speed;
    int8_t right_motor_speed;
    
    // 系统状态
    LineFollowing_State_t system_state;
    uint32_t update_frequency;
    uint32_t error_count;
} DebugData_t;
```

### 9.2 OLED显示集成
```c
// OLED显示循迹状态
void LineFollowing_DisplayStatus(void) {
    DebugData_t debug_data;
    LineFollowing_GetDebugData(&debug_data);
    
    // 第1行：目标角度和当前角度
    OLED_ShowStr(0, 0, "T:", 8);
    OLED_ShowSignedAngle(16, 0, (int16_t)debug_data.target_angle);
    OLED_ShowStr(48, 0, "C:", 8);
    OLED_ShowSignedAngle(64, 0, (int16_t)debug_data.current_yaw);
    
    // 第2行：角度误差和线路误差
    OLED_ShowStr(0, 1, "AE:", 8);
    OLED_ShowSignedAngle(24, 1, (int16_t)debug_data.angle_error);
    OLED_ShowStr(56, 1, "LE:", 8);
    OLED_ShowSignedAngle(80, 1, (int16_t)(debug_data.line_error * 10));
    
    // 第3行：电机速度
    OLED_ShowStr(0, 2, "L:", 8);
    OLED_ShowSignedNum(16, 2, debug_data.left_motor_speed, 3, 8);
    OLED_ShowStr(48, 2, "R:", 8);
    OLED_ShowSignedNum(64, 2, debug_data.right_motor_speed, 3, 8);
    
    // 第4行：系统状态
    const char* state_str = LineFollowing_GetStateString();
    OLED_ShowStr(0, 3, (uint8_t*)state_str, 8);
}
```

---

## 10. 技术决策记录 (ADR)

### ADR-001: 控制算法选择
**决策**: 使用PID控制算法  
**理由**: 成熟稳定，参数调节相对简单，适合实时控制  
**替代方案**: 模糊控制、神经网络控制  
**影响**: 需要手动调节PID参数

### ADR-002: 数据融合策略
**决策**: 简单加权融合  
**理由**: 计算量小，实时性好，易于理解和调试  
**替代方案**: 卡尔曼滤波、粒子滤波  
**影响**: 融合效果可能不如高级算法

### ADR-003: 模块化设计
**决策**: 采用分层模块化架构  
**理由**: 便于维护、测试和扩展  
**替代方案**: 单体架构  
**影响**: 增加了一定的复杂度

---

**文档结束**

> **备注**：本架构设计文档基于Emma的PRD需求制定，所有技术方案都经过可行性评估，确保能够在STM32F407平台上稳定运行。
