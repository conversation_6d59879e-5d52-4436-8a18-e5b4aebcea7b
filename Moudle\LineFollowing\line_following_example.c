/**********************************************************
***	循迹功能集成示例代码
***	编写作者：Alex (工程师)
***	功能：展示如何在main.c中集成循迹功能
***	版本：V1.0 - 完整示例版
***	日期：2025-07-29
**********************************************************/

#include "main.h"
#include "line_following.h"
#include "oled.h"

// 外部变量声明 (这些变量应该在main.c中定义)
extern I2C_HandleTypeDef hi2c1;
extern I2C_HandleTypeDef hi2c2;
extern TIM_HandleTypeDef htim1;
extern UART_HandleTypeDef huart4;
extern UART_HandleTypeDef huart5;
extern UART_HandleTypeDef huart2;

// JY901S相关变量
extern JY901S_Handle jy901s_handle;
extern JY901S_Data sensor_data;
extern uint8_t uart_rx_buffer[1];

// 灰度传感器相关变量
extern uint8_t gray_digital_data;
extern uint8_t gray_analog_data[8];
extern uint8_t gray_sensor_connected;

// 电机相关变量
extern Motor_Handle motor_left;
extern Motor_Handle motor_right;

/**********************************************************
*** 循迹功能集成示例
**********************************************************/

/**
  * @brief 循迹功能初始化示例
  * @note 在main()函数的USER CODE BEGIN 2部分调用
  */
void LineFollowing_Example_Init(void)
{
    // 1. 初始化硬件模块 (按照现有代码顺序)
    
    // OLED初始化 (最先初始化，用于显示状态)
    OLED_Init();
    OLED_Clear();
    OLED_ShowStr(0, 0, "LineFollow Init...", 8);
    
    // JY901S姿态传感器初始化
    JY901S_Init(&jy901s_handle, &huart5, NULL);
    HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
    
    // 灰度传感器初始化
    GrayScale_Init();
    
    // DRV8871电机驱动初始化
    Motor_Init();
    
    // 2. 初始化循迹功能
    LineFollowing_Config_t config;
    
    // 使用自定义配置 (可选，也可以传NULL使用默认配置)
    config.angle_kp = 2.5f;     // 角度控制比例系数
    config.angle_ki = 0.1f;     // 角度控制积分系数
    config.angle_kd = 0.8f;     // 角度控制微分系数
    config.angle_output_limit = 40.0f;
    
    config.line_kp = 1.8f;      // 巡线控制比例系数
    config.line_ki = 0.05f;     // 巡线控制积分系数
    config.line_kd = 0.4f;      // 巡线控制微分系数
    config.line_output_limit = 35.0f;
    
    // 传感器权重配置 (从左到右，外侧权重更大)
    config.line_weights[0] = -4.0f;  // 最左侧，强左转
    config.line_weights[1] = -2.5f;  // 左侧，中左转
    config.line_weights[2] = -1.2f;  // 左内侧，微左转
    config.line_weights[3] = -0.6f;  // 左中心，微左转
    config.line_weights[4] = 0.6f;   // 右中心，微右转
    config.line_weights[5] = 1.2f;   // 右内侧，微右转
    config.line_weights[6] = 2.5f;   // 右侧，中右转
    config.line_weights[7] = 4.0f;   // 最右侧，强右转
    
    config.base_speed = 45;          // 基础速度45%
    config.max_correction = 25;      // 最大修正25%
    config.mode = FUSION;            // 融合控制模式
    config.update_period = 50;       // 50ms更新周期 (20Hz)
    config.debug_enabled = true;
    
    // 初始化循迹系统
    if (LineFollowing_Init(&config) == HAL_OK) {
        OLED_Clear();
        OLED_ShowStr(0, 0, "LineFollow Ready!", 8);
        HAL_Delay(1000);
    } else {
        OLED_Clear();
        OLED_ShowStr(0, 0, "LineFollow Error!", 8);
        while(1); // 初始化失败，停止运行
    }
}

/**
  * @brief 循迹功能主循环示例
  * @note 在main()函数的while(1)循环中调用
  */
void LineFollowing_Example_MainLoop(void)
{
    static uint32_t last_display_update = 0;
    static uint32_t control_start_time = 0;
    static bool control_started = false;
    
    uint32_t current_time = HAL_GetTick();
    
    // 1. 循迹控制更新 (高频率调用)
    LineFollowing_Update();
    
    // 2. 延时启动控制 (给传感器预热时间)
    if (!control_started && current_time > 2000) {
        // 设置目标角度为0度 (直线行驶)
        LineFollowing_SetTargetAngle(0.0f);
        
        // 启动循迹功能
        LineFollowing_Start();
        
        control_started = true;
        control_start_time = current_time;
        
        OLED_Clear();
        OLED_ShowStr(0, 0, "LineFollow Start!", 8);
    }
    
    // 3. OLED显示更新 (低频率更新，避免影响控制性能)
    if (current_time - last_display_update >= 200) { // 200ms更新一次显示
        OLED_Clear();
        LineFollowing_DisplayStatus();
        last_display_update = current_time;
    }
    
    // 4. 示例：定时改变目标角度 (演示转弯功能)
    if (control_started && (current_time - control_start_time) > 10000) {
        static uint8_t angle_step = 0;
        static uint32_t last_angle_change = 0;
        
        if (current_time - last_angle_change > 5000) { // 每5秒改变一次角度
            switch (angle_step) {
                case 0:
                    LineFollowing_SetTargetAngle(0.0f);    // 直行
                    break;
                case 1:
                    LineFollowing_SetTargetAngle(90.0f);   // 右转90度
                    break;
                case 2:
                    LineFollowing_SetTargetAngle(180.0f);  // 转向180度
                    break;
                case 3:
                    LineFollowing_SetTargetAngle(-90.0f);  // 左转90度
                    break;
                default:
                    angle_step = 0;
                    continue;
            }
            
            angle_step++;
            if (angle_step > 3) angle_step = 0;
            last_angle_change = current_time;
        }
    }
    
    // 5. 主循环延时 (控制主循环频率)
    HAL_Delay(10); // 10ms延时，实现约100Hz主循环
}

/**********************************************************
*** 循迹功能测试示例
**********************************************************/

/**
  * @brief 循迹功能基础测试
  * @note 可以在main()中调用此函数进行功能测试
  */
void LineFollowing_Example_BasicTest(void)
{
    OLED_Clear();
    OLED_ShowStr(0, 0, "Basic Test Start", 8);
    HAL_Delay(2000);
    
    // 测试1：纯角度控制模式
    OLED_Clear();
    OLED_ShowStr(0, 0, "Test: Angle Only", 8);
    
    LineFollowing_SetMode(ANGLE_ONLY);
    LineFollowing_SetTargetAngle(0.0f);
    LineFollowing_SetBaseSpeed(30);
    LineFollowing_Start();
    
    // 运行5秒
    uint32_t test_start = HAL_GetTick();
    while (HAL_GetTick() - test_start < 5000) {
        LineFollowing_Update();
        
        if (HAL_GetTick() % 200 == 0) {
            OLED_Clear();
            LineFollowing_DisplayStatus();
        }
        
        HAL_Delay(10);
    }
    
    LineFollowing_Stop();
    HAL_Delay(1000);
    
    // 测试2：纯巡线控制模式
    OLED_Clear();
    OLED_ShowStr(0, 0, "Test: Line Only", 8);
    
    LineFollowing_SetMode(LINE_ONLY);
    LineFollowing_SetBaseSpeed(25);
    LineFollowing_Start();
    
    // 运行5秒
    test_start = HAL_GetTick();
    while (HAL_GetTick() - test_start < 5000) {
        LineFollowing_Update();
        
        if (HAL_GetTick() % 200 == 0) {
            OLED_Clear();
            LineFollowing_DisplayStatus();
        }
        
        HAL_Delay(10);
    }
    
    LineFollowing_Stop();
    HAL_Delay(1000);
    
    // 测试3：融合控制模式
    OLED_Clear();
    OLED_ShowStr(0, 0, "Test: Fusion", 8);
    
    LineFollowing_SetMode(FUSION);
    LineFollowing_SetTargetAngle(0.0f);
    LineFollowing_SetBaseSpeed(40);
    LineFollowing_Start();
    
    // 运行10秒
    test_start = HAL_GetTick();
    while (HAL_GetTick() - test_start < 10000) {
        LineFollowing_Update();
        
        if (HAL_GetTick() % 200 == 0) {
            OLED_Clear();
            LineFollowing_DisplayStatus();
        }
        
        HAL_Delay(10);
    }
    
    LineFollowing_Stop();
    
    OLED_Clear();
    OLED_ShowStr(0, 0, "Test Complete!", 8);
}

/**********************************************************
*** 在main.c中的集成示例
**********************************************************/

/*
// 在main.c中的集成示例：

int main(void)
{
  // MCU Configuration
  HAL_Init();
  SystemClock_Config();

  // Initialize all configured peripherals
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C2_Init();
  MX_I2C1_Init();
  MX_UART5_Init();        // JY901S通信
  MX_TIM1_Init();         // 电机PWM
  MX_USART2_UART_Init();  // EMMV5 X轴通信
  MX_UART4_Init();        // EMMV5 Y轴通信

  // USER CODE BEGIN 2
  
  // 初始化循迹功能
  LineFollowing_Example_Init();
  
  // 可选：运行基础测试
  // LineFollowing_Example_BasicTest();

  // USER CODE END 2

  // Infinite loop
  while (1)
  {
    // USER CODE BEGIN 3
    
    // 循迹功能主循环
    LineFollowing_Example_MainLoop();
    
    // USER CODE END 3
  }
}

// UART接收中断回调函数 (已存在，无需修改)
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if (huart->Instance == UART5)
  {
    // 处理JY901S数据
    JY901S_ProcessByte(&jy901s_handle, uart_rx_buffer[0]);
    HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
  }
}

*/
